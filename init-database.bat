@echo off
chcp 65001 >nul
echo ========================================
echo    数据库初始化脚本
echo ========================================
echo.

echo 正在初始化文件监控系统数据库...
echo.

echo 请确保 MySQL 服务正在运行，并且您有管理员权限。
echo.
echo 数据库配置:
echo   服务器: 127.0.0.1
echo   用户名: root
echo   密码: 521223
echo   数据库: FileMonitorDB
echo.

set /p confirm="确认要初始化数据库吗? (Y/N): "
if /i not "%confirm%"=="Y" (
    echo 操作已取消
    pause
    exit /b
)

echo.
echo 正在执行数据库初始化...

mysql -h 127.0.0.1 -u root -p521223 < Database\init_database.sql

if %errorlevel% equ 0 (
    echo.
    echo ✅ 数据库初始化成功！
    echo.
    echo 已创建的表:
    echo   - access_log: 文件访问日志表
    echo   - alert_history: 告警历史表
    echo   - monitor_rules: 监控规则表
    echo   - system_config: 系统配置表
    echo   - performance_stats: 性能统计表
    echo.
    echo 现在可以启动文件监控系统了。
) else (
    echo.
    echo ❌ 数据库初始化失败！
    echo.
    echo 可能的原因:
    echo   1. MySQL 服务未启动
    echo   2. 用户名或密码错误
    echo   3. 权限不足
    echo.
    echo 请检查 MySQL 配置后重试。
)

echo.
pause
