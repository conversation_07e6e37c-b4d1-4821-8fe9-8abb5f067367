# 文件监控规则配置文件

global:
  defaultAlertLevel: Medium
  enableAllRules: true
  logRetentionDays: 90
  maxAlertsPerMinute: 10

rules:
  - name: "敏感文件访问告警"
    description: "检测对包含敏感关键词的文件访问"
    enabled: true
    level: High
    conditions:
      pathPattern: ".*[机密|保密|confidential|secret|财务|finance|salary|工资].*\\.(xlsx?|docx?|pdf|txt)$"
      actions: ["Read", "Write", "Delete"]
      userPattern: "^(?!.*ADMIN).*$"  # 排除管理员用户
    alert:
      methods: ["email", "log"]
      recipients: ["<EMAIL>", "<EMAIL>"]
      messageTemplate: "⚠️ 敏感文件访问告警: 用户 {UserName} 在 {EventTime} 对文件 {FilePath} 执行了 {Action} 操作"
      throttleSeconds: 300

  - name: "非工作时间文件访问"
    description: "检测非工作时间的文件访问行为"
    enabled: true
    level: Medium
    conditions:
      timeRange: "18:00-08:00"  # 晚6点到早8点
      actions: ["Read", "Write", "Delete"]
    alert:
      methods: ["email"]
      recipients: ["<EMAIL>"]
      messageTemplate: "🕐 非工作时间访问: 用户 {UserName} 在 {EventTime} 访问了 {FilePath}"
      throttleSeconds: 600

  - name: "批量删除操作告警"
    description: "检测可能的批量删除操作"
    enabled: true
    level: Critical
    conditions:
      actions: ["Delete"]
    alert:
      methods: ["email", "log"]
      recipients: ["<EMAIL>"]
      messageTemplate: "🚨 批量删除告警: 用户 {UserName} 删除了文件 {FilePath}"
      throttleSeconds: 60

  - name: "外部IP访问告警"
    description: "检测来自外部IP的文件访问"
    enabled: true
    level: High
    conditions:
      ipPattern: "^(?!192\\.168\\.|10\\.|172\\.(1[6-9]|2[0-9]|3[01])\\.).*"
      actions: ["Read", "Write"]
    alert:
      methods: ["email"]
      recipients: ["<EMAIL>"]
      messageTemplate: "🌐 外部IP访问: IP {SourceIP} 的用户 {UserName} 访问了 {FilePath}"
      throttleSeconds: 300

  - name: "大文件操作告警"
    description: "检测对大文件的操作"
    enabled: true
    level: Medium
    conditions:
      minFileSize: 104857600  # 100MB
      actions: ["Write", "Delete"]
    alert:
      methods: ["log"]
      messageTemplate: "📁 大文件操作: 用户 {UserName} 对大文件 {FilePath} 执行了 {Action} 操作"
      throttleSeconds: 300

  - name: "可执行文件访问告警"
    description: "检测对可执行文件的访问"
    enabled: true
    level: High
    conditions:
      fileExtensions: ["exe", "bat", "cmd", "ps1", "vbs", "scr"]
      actions: ["Write", "Create"]
    alert:
      methods: ["email", "log"]
      recipients: ["<EMAIL>"]
      messageTemplate: "⚡ 可执行文件操作: 用户 {UserName} 对可执行文件 {FilePath} 执行了 {Action} 操作"
      throttleSeconds: 180

  - name: "系统文件访问告警"
    description: "检测对系统关键文件的访问"
    enabled: true
    level: Critical
    conditions:
      pathPattern: ".*(Windows|System32|Program Files|boot\\.ini|ntldr).*"
      actions: ["Write", "Delete"]
    alert:
      methods: ["email", "log"]
      recipients: ["<EMAIL>"]
      messageTemplate: "🔒 系统文件访问: 用户 {UserName} 对系统文件 {FilePath} 执行了 {Action} 操作"
      throttleSeconds: 60

  - name: "频繁访问告警"
    description: "检测同一用户对同一文件的频繁访问"
    enabled: false  # 需要额外的逻辑支持，暂时禁用
    level: Medium
    conditions:
      actions: ["Read"]
    alert:
      methods: ["log"]
      messageTemplate: "🔄 频繁访问: 用户 {UserName} 频繁访问文件 {FilePath}"
      throttleSeconds: 900

  - name: "周末访问告警"
    description: "检测周末的文件访问行为"
    enabled: true
    level: Low
    conditions:
      # 注意：这里需要在代码中实现周末检测逻辑
      actions: ["Read", "Write", "Delete"]
    alert:
      methods: ["log"]
      messageTemplate: "📅 周末访问: 用户 {UserName} 在周末访问了 {FilePath}"
      throttleSeconds: 1800

  - name: "数据库文件访问告警"
    description: "检测对数据库文件的直接访问"
    enabled: true
    level: High
    conditions:
      fileExtensions: ["mdb", "accdb", "db", "sqlite", "bak"]
      actions: ["Read", "Write", "Delete"]
    alert:
      methods: ["email"]
      recipients: ["<EMAIL>", "<EMAIL>"]
      messageTemplate: "🗄️ 数据库文件访问: 用户 {UserName} 访问了数据库文件 {FilePath}"
      throttleSeconds: 300
