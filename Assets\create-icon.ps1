# 创建文件监控系统图标
# 使用 PowerShell 和 .NET 绘图 API 创建简单的图标

param(
    [string]$OutputPath = "icon.ico",
    [int]$Size = 256
)

Add-Type -AssemblyName System.Drawing
Add-Type -AssemblyName System.Windows.Forms

Write-Host "=== 文件监控系统图标生成器 ===" -ForegroundColor Green
Write-Host "输出路径: $OutputPath"
Write-Host "图标大小: ${Size}x${Size}"
Write-Host ""

try {
    # 创建位图
    $bitmap = New-Object System.Drawing.Bitmap($Size, $Size)
    $graphics = [System.Drawing.Graphics]::FromImage($bitmap)
    
    # 设置高质量渲染
    $graphics.SmoothingMode = [System.Drawing.Drawing2D.SmoothingMode]::AntiAlias
    $graphics.TextRenderingHint = [System.Drawing.Text.TextRenderingHint]::AntiAlias
    
    # 背景渐变 (深蓝到浅蓝)
    $brush = New-Object System.Drawing.Drawing2D.LinearGradientBrush(
        [System.Drawing.Point]::new(0, 0),
        [System.Drawing.Point]::new($Size, $Size),
        [System.Drawing.Color]::FromArgb(255, 30, 58, 138),  # 深蓝
        [System.Drawing.Color]::FromArgb(255, 59, 130, 246)  # 浅蓝
    )
    
    # 绘制圆角矩形背景
    $rect = New-Object System.Drawing.Rectangle(0, 0, $Size, $Size)
    $path = New-Object System.Drawing.Drawing2D.GraphicsPath
    $radius = $Size / 8
    
    # 创建圆角矩形路径
    $path.AddArc($rect.X, $rect.Y, $radius * 2, $radius * 2, 180, 90)
    $path.AddArc($rect.Right - $radius * 2, $rect.Y, $radius * 2, $radius * 2, 270, 90)
    $path.AddArc($rect.Right - $radius * 2, $rect.Bottom - $radius * 2, $radius * 2, $radius * 2, 0, 90)
    $path.AddArc($rect.X, $rect.Bottom - $radius * 2, $radius * 2, $radius * 2, 90, 90)
    $path.CloseFigure()
    
    $graphics.FillPath($brush, $path)
    
    # 绘制盾牌图标 (安全象征)
    $shieldBrush = New-Object System.Drawing.SolidBrush([System.Drawing.Color]::White)
    $shieldPen = New-Object System.Drawing.Pen([System.Drawing.Color]::FromArgb(200, 255, 255, 255), 3)
    
    # 盾牌路径
    $centerX = $Size / 2
    $centerY = $Size / 2
    $shieldWidth = $Size * 0.4
    $shieldHeight = $Size * 0.5
    
    $shieldPath = New-Object System.Drawing.Drawing2D.GraphicsPath
    $shieldPath.AddArc($centerX - $shieldWidth/2, $centerY - $shieldHeight/2, $shieldWidth, $shieldHeight/2, 0, 180)
    $shieldPath.AddLine($centerX - $shieldWidth/2, $centerY, $centerX, $centerY + $shieldHeight/2)
    $shieldPath.AddLine($centerX, $centerY + $shieldHeight/2, $centerX + $shieldWidth/2, $centerY)
    $shieldPath.CloseFigure()
    
    $graphics.FillPath($shieldBrush, $shieldPath)
    $graphics.DrawPath($shieldPen, $shieldPath)
    
    # 绘制文件图标 (在盾牌内)
    $fileBrush = New-Object System.Drawing.SolidBrush([System.Drawing.Color]::FromArgb(255, 30, 58, 138))
    $fileWidth = $shieldWidth * 0.4
    $fileHeight = $shieldHeight * 0.5
    $fileX = $centerX - $fileWidth/2
    $fileY = $centerY - $fileHeight/2
    
    # 文件矩形
    $fileRect = New-Object System.Drawing.Rectangle($fileX, $fileY, $fileWidth, $fileHeight)
    $graphics.FillRectangle($fileBrush, $fileRect)
    
    # 文件折角
    $cornerSize = $fileWidth * 0.2
    $cornerPoints = @(
        [System.Drawing.Point]::new($fileX + $fileWidth - $cornerSize, $fileY),
        [System.Drawing.Point]::new($fileX + $fileWidth, $fileY + $cornerSize),
        [System.Drawing.Point]::new($fileX + $fileWidth - $cornerSize, $fileY + $cornerSize)
    )
    $graphics.FillPolygon($shieldBrush, $cornerPoints)
    
    # 文件内容线条
    $linePen = New-Object System.Drawing.Pen([System.Drawing.Color]::White, 2)
    $lineY1 = $fileY + $fileHeight * 0.4
    $lineY2 = $fileY + $fileHeight * 0.6
    $lineY3 = $fileY + $fileHeight * 0.8
    $lineX1 = $fileX + $fileWidth * 0.15
    $lineX2 = $fileX + $fileWidth * 0.85
    
    $graphics.DrawLine($linePen, $lineX1, $lineY1, $lineX2, $lineY1)
    $graphics.DrawLine($linePen, $lineX1, $lineY2, $lineX2 * 0.8, $lineY2)
    $graphics.DrawLine($linePen, $lineX1, $lineY3, $lineX2 * 0.6, $lineY3)
    
    # 添加监控眼睛图标 (右上角)
    $eyeSize = $Size * 0.15
    $eyeX = $Size * 0.75
    $eyeY = $Size * 0.25
    $eyeBrush = New-Object System.Drawing.SolidBrush([System.Drawing.Color]::FromArgb(200, 255, 255, 255))
    $eyeRect = New-Object System.Drawing.Rectangle($eyeX - $eyeSize/2, $eyeY - $eyeSize/3, $eyeSize, $eyeSize/1.5)
    
    $graphics.FillEllipse($eyeBrush, $eyeRect)
    
    # 眼球
    $pupilBrush = New-Object System.Drawing.SolidBrush([System.Drawing.Color]::FromArgb(255, 30, 58, 138))
    $pupilSize = $eyeSize * 0.4
    $pupilRect = New-Object System.Drawing.Rectangle($eyeX - $pupilSize/2, $eyeY - $pupilSize/2, $pupilSize, $pupilSize)
    $graphics.FillEllipse($pupilBrush, $pupilRect)
    
    # 保存为多尺寸 ICO 文件
    Write-Host "正在生成图标..." -ForegroundColor Yellow
    
    # 创建不同尺寸的图标
    $sizes = @(16, 24, 32, 48, 64, 128, 256)
    $icons = @()
    
    foreach ($iconSize in $sizes) {
        $scaledBitmap = New-Object System.Drawing.Bitmap($iconSize, $iconSize)
        $scaledGraphics = [System.Drawing.Graphics]::FromImage($scaledBitmap)
        $scaledGraphics.InterpolationMode = [System.Drawing.Drawing2D.InterpolationMode]::HighQualityBicubic
        $scaledGraphics.DrawImage($bitmap, 0, 0, $iconSize, $iconSize)
        $icons += $scaledBitmap
        $scaledGraphics.Dispose()
    }
    
    # 保存 ICO 文件 (简化版本，保存为 PNG 然后转换)
    $pngPath = $OutputPath -replace '\.ico$', '.png'
    $bitmap.Save($pngPath, [System.Drawing.Imaging.ImageFormat]::Png)
    
    Write-Host "✅ 图标已生成: $pngPath" -ForegroundColor Green
    Write-Host "注意: 已生成 PNG 格式，可使用在线工具转换为 ICO 格式" -ForegroundColor Yellow
    
    # 清理资源
    $graphics.Dispose()
    $bitmap.Dispose()
    $brush.Dispose()
    $shieldBrush.Dispose()
    $shieldPen.Dispose()
    $fileBrush.Dispose()
    $linePen.Dispose()
    $eyeBrush.Dispose()
    $pupilBrush.Dispose()
    $path.Dispose()
    $shieldPath.Dispose()
    
    foreach ($icon in $icons) {
        $icon.Dispose()
    }
    
    Write-Host ""
    Write-Host "图标设计说明:" -ForegroundColor Cyan
    Write-Host "- 🛡️ 盾牌: 代表安全防护"
    Write-Host "- 📄 文件: 代表文件监控"
    Write-Host "- 👁️ 眼睛: 代表实时监控"
    Write-Host "- 🔵 蓝色渐变: 代表专业和可靠"
    
} catch {
    Write-Error "生成图标时发生错误: $($_.Exception.Message)"
    exit 1
}

Write-Host ""
Write-Host "=== 图标生成完成 ===" -ForegroundColor Green
