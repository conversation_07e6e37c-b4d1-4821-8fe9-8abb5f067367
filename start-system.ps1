# 文件监控系统启动脚本
param(
    [string]$WebPort = "5001",
    [switch]$SkipBrowser = $false,
    [switch]$TestMode = $false
)

Write-Host "========================================" -ForegroundColor Cyan
Write-Host "    文件监控系统启动器" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

# 检查 .NET 运行时
Write-Host "🔍 检查运行环境..." -ForegroundColor Yellow
try {
    $dotnetVersion = dotnet --version
    Write-Host "✅ .NET 版本: $dotnetVersion" -ForegroundColor Green
} catch {
    Write-Host "❌ 未找到 .NET 运行时，请先安装 .NET 8.0 或更高版本" -ForegroundColor Red
    exit 1
}

# 检查项目文件
$webProject = "FileMonitor.Web\FileMonitor.Web.csproj"
if (-not (Test-Path $webProject)) {
    Write-Host "❌ 找不到 Web 项目文件: $webProject" -ForegroundColor Red
    exit 1
}

Write-Host ""
Write-Host "🚀 启动文件监控系统..." -ForegroundColor Green
Write-Host ""

try {
    # 启动 Web 应用
    Write-Host "[1/2] 启动 Web 管理界面..." -ForegroundColor Yellow
    Write-Host "      端口: $WebPort" -ForegroundColor Gray
    
    $webUrl = "http://localhost:$WebPort"
    
    if ($TestMode) {
        Write-Host "🧪 测试模式: 编译项目..." -ForegroundColor Cyan
        dotnet build FileMonitor.Web --verbosity quiet
        if ($LASTEXITCODE -ne 0) {
            throw "项目编译失败"
        }
    }
    
    # 启动 Web 应用（后台运行）
    $webProcess = Start-Process -FilePath "dotnet" -ArgumentList "run", "--project", "FileMonitor.Web", "--urls", $webUrl -PassThru -WindowStyle Hidden
    
    Write-Host "[2/2] 等待服务启动..." -ForegroundColor Yellow
    Start-Sleep -Seconds 8
    
    # 检查服务是否启动成功
    Write-Host "🔍 检查服务状态..." -ForegroundColor Yellow
    try {
        $response = Invoke-WebRequest -Uri $webUrl -TimeoutSec 10 -UseBasicParsing
        Write-Host "✅ Web 服务启动成功！" -ForegroundColor Green
    } catch {
        Write-Host "⚠️  无法验证服务状态，但进程已启动" -ForegroundColor Yellow
    }
    
    Write-Host ""
    Write-Host "🎉 系统启动完成！" -ForegroundColor Green
    Write-Host ""
    Write-Host "📊 Web 管理界面: $webUrl" -ForegroundColor Cyan
    Write-Host ""
    Write-Host "🔧 功能模块:" -ForegroundColor White
    Write-Host "   📈 仪表板 - 系统概览和实时统计" -ForegroundColor Gray
    Write-Host "   📋 访问日志 - 查看和搜索文件访问记录" -ForegroundColor Gray
    Write-Host "   🚨 告警管理 - 查看和处理安全告警" -ForegroundColor Gray
    Write-Host "   ⚙️  系统管理 - 配置和监控系统状态" -ForegroundColor Gray
    Write-Host ""
    Write-Host "💡 重要提示:" -ForegroundColor Yellow
    Write-Host "   • 确保 MySQL 数据库服务正在运行" -ForegroundColor White
    Write-Host "   • 首次使用请先运行: Database\init_database.sql" -ForegroundColor White
    Write-Host "   • 可以修改 Rules\rules.yaml 来自定义监控规则" -ForegroundColor White
    Write-Host "   • 按 Ctrl+C 停止服务" -ForegroundColor White
    Write-Host ""
    
    # 打开浏览器
    if (-not $SkipBrowser) {
        Write-Host "🌐 正在打开 Web 界面..." -ForegroundColor Green
        Start-Process $webUrl
    }
    
    Write-Host "服务正在运行中，按 Ctrl+C 停止..." -ForegroundColor Cyan
    
    # 等待用户中断
    try {
        while ($true) {
            Start-Sleep -Seconds 1
            if ($webProcess.HasExited) {
                Write-Host "Web 服务已停止" -ForegroundColor Yellow
                break
            }
        }
    } catch {
        Write-Host ""
        Write-Host "正在停止服务..." -ForegroundColor Yellow
    }
    
} catch {
    Write-Host ""
    Write-Host "❌ 启动失败: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
} finally {
    # 清理进程
    if ($webProcess -and -not $webProcess.HasExited) {
        Write-Host "正在停止 Web 服务..." -ForegroundColor Yellow
        $webProcess.Kill()
        $webProcess.WaitForExit(5000)
    }
    
    Write-Host ""
    Write-Host "🛑 文件监控系统已停止" -ForegroundColor Red
}
