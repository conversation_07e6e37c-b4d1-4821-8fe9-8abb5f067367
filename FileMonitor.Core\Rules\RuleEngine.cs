using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using FileMonitor.Core.Models;
using Microsoft.Extensions.Logging;
using YamlDotNet.Serialization;
using YamlDotNet.Serialization.NamingConventions;

namespace FileMonitor.Core.Rules
{
    /// <summary>
    /// 规则引擎类
    /// </summary>
    public class RuleEngine
    {
        private readonly ILogger<RuleEngine> _logger;
        private readonly Dictionary<string, DateTime> _alertThrottleCache;
        private RulesConfig _rulesConfig;
        private readonly object _lockObject = new object();

        public event EventHandler<AlertTriggeredEventArgs>? AlertTriggered;

        public RuleEngine(ILogger<RuleEngine> logger)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _alertThrottleCache = new Dictionary<string, DateTime>();
            _rulesConfig = new RulesConfig();
        }

        /// <summary>
        /// 从YAML文件加载规则
        /// </summary>
        /// <param name="filePath">YAML文件路径</param>
        public async Task LoadRulesFromFileAsync(string filePath)
        {
            try
            {
                if (!File.Exists(filePath))
                {
                    _logger.LogWarning("规则文件不存在: {FilePath}", filePath);
                    return;
                }

                var yamlContent = await File.ReadAllTextAsync(filePath);
                var deserializer = new DeserializerBuilder()
                    .WithNamingConvention(CamelCaseNamingConvention.Instance)
                    .Build();

                _rulesConfig = deserializer.Deserialize<RulesConfig>(yamlContent);
                _logger.LogInformation("成功加载 {Count} 条规则", _rulesConfig.Rules.Count);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "加载规则文件失败: {FilePath}", filePath);
                throw;
            }
        }

        /// <summary>
        /// 评估文件访问日志是否触发规则
        /// </summary>
        /// <param name="log">文件访问日志</param>
        public async Task EvaluateLogAsync(FileAccessLog log)
        {
            if (!_rulesConfig.Global.EnableAllRules)
                return;

            foreach (var rule in _rulesConfig.Rules.Where(r => r.Enabled))
            {
                try
                {
                    if (await EvaluateRuleAsync(rule, log))
                    {
                        await TriggerAlertAsync(rule, log);
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "评估规则时发生错误: {RuleName}", rule.Name);
                }
            }
        }

        /// <summary>
        /// 评估单个规则
        /// </summary>
        private async Task<bool> EvaluateRuleAsync(MonitorRule rule, FileAccessLog log)
        {
            var conditions = rule.Conditions;

            // 检查文件路径模式
            if (!string.IsNullOrEmpty(conditions.PathPattern))
            {
                if (!Regex.IsMatch(log.FilePath, conditions.PathPattern, RegexOptions.IgnoreCase))
                    return false;
            }

            // 检查用户模式
            if (!string.IsNullOrEmpty(conditions.UserPattern))
            {
                if (!Regex.IsMatch(log.UserName, conditions.UserPattern, RegexOptions.IgnoreCase))
                    return false;
            }

            // 检查操作类型
            if (conditions.Actions.Any())
            {
                var logActions = log.Action.Split(',', StringSplitOptions.RemoveEmptyEntries);
                if (!logActions.Any(action => conditions.Actions.Contains(action.Trim(), StringComparer.OrdinalIgnoreCase)))
                    return false;
            }

            // 检查时间范围
            if (!string.IsNullOrEmpty(conditions.TimeRange))
            {
                if (!IsTimeInRange(log.EventTime, conditions.TimeRange))
                    return false;
            }

            // 检查IP地址模式
            if (!string.IsNullOrEmpty(conditions.IpPattern))
            {
                if (!Regex.IsMatch(log.SourceIP, conditions.IpPattern))
                    return false;
            }

            // 检查文件扩展名
            if (conditions.FileExtensions.Any())
            {
                var fileExtension = Path.GetExtension(log.FilePath).TrimStart('.');
                if (!conditions.FileExtensions.Contains(fileExtension, StringComparer.OrdinalIgnoreCase))
                    return false;
            }

            // 检查文件大小（如果可获取）
            if (conditions.MinFileSize.HasValue || conditions.MaxFileSize.HasValue)
            {
                var fileSize = await GetFileSizeAsync(log.FilePath);
                if (fileSize.HasValue)
                {
                    if (conditions.MinFileSize.HasValue && fileSize < conditions.MinFileSize)
                        return false;
                    if (conditions.MaxFileSize.HasValue && fileSize > conditions.MaxFileSize)
                        return false;
                }
            }

            return true;
        }

        /// <summary>
        /// 检查时间是否在指定范围内
        /// </summary>
        private bool IsTimeInRange(DateTime dateTime, string timeRange)
        {
            try
            {
                var parts = timeRange.Split('-');
                if (parts.Length != 2)
                    return true;

                var startTime = TimeSpan.Parse(parts[0].Trim());
                var endTime = TimeSpan.Parse(parts[1].Trim());
                var currentTime = dateTime.TimeOfDay;

                if (startTime <= endTime)
                {
                    return currentTime >= startTime && currentTime <= endTime;
                }
                else
                {
                    // 跨越午夜的时间范围
                    return currentTime >= startTime || currentTime <= endTime;
                }
            }
            catch
            {
                return true; // 如果解析失败，默认通过
            }
        }

        /// <summary>
        /// 获取文件大小
        /// </summary>
        private async Task<long?> GetFileSizeAsync(string filePath)
        {
            try
            {
                if (File.Exists(filePath))
                {
                    var fileInfo = new FileInfo(filePath);
                    return fileInfo.Length;
                }
                return null;
            }
            catch
            {
                return null;
            }
        }

        /// <summary>
        /// 触发告警
        /// </summary>
        private async Task TriggerAlertAsync(MonitorRule rule, FileAccessLog log)
        {
            // 检查告警频率限制
            var throttleKey = $"{rule.Name}_{log.FilePath}_{log.UserName}";
            lock (_lockObject)
            {
                if (_alertThrottleCache.TryGetValue(throttleKey, out var lastAlertTime))
                {
                    if (DateTime.Now.Subtract(lastAlertTime).TotalSeconds < rule.Alert.ThrottleSeconds)
                    {
                        return; // 在限制时间内，跳过告警
                    }
                }
                _alertThrottleCache[throttleKey] = DateTime.Now;
            }

            // 创建告警事件参数
            var alertArgs = new AlertTriggeredEventArgs
            {
                Rule = rule,
                Log = log,
                AlertTime = DateTime.Now,
                Message = FormatAlertMessage(rule.Alert.MessageTemplate, log)
            };

            // 触发告警事件
            AlertTriggered?.Invoke(this, alertArgs);

            _logger.LogWarning("规则触发告警: {RuleName} - {Message}", rule.Name, alertArgs.Message);
        }

        /// <summary>
        /// 格式化告警消息
        /// </summary>
        private string FormatAlertMessage(string template, FileAccessLog log)
        {
            return template
                .Replace("{FilePath}", log.FilePath)
                .Replace("{UserName}", log.UserName)
                .Replace("{Action}", log.Action)
                .Replace("{EventTime}", log.EventTime.ToString("yyyy-MM-dd HH:mm:ss"))
                .Replace("{SourceIP}", log.SourceIP);
        }

        /// <summary>
        /// 清理过期的告警限制缓存
        /// </summary>
        public void CleanupThrottleCache()
        {
            lock (_lockObject)
            {
                var expiredKeys = _alertThrottleCache
                    .Where(kvp => DateTime.Now.Subtract(kvp.Value).TotalHours > 24)
                    .Select(kvp => kvp.Key)
                    .ToList();

                foreach (var key in expiredKeys)
                {
                    _alertThrottleCache.Remove(key);
                }

                if (expiredKeys.Count > 0)
                {
                    _logger.LogDebug("清理了 {Count} 个过期的告警限制缓存", expiredKeys.Count);
                }
            }
        }

        /// <summary>
        /// 获取当前加载的规则数量
        /// </summary>
        public int GetRuleCount()
        {
            return _rulesConfig.Rules.Count;
        }

        /// <summary>
        /// 获取启用的规则数量
        /// </summary>
        public int GetEnabledRuleCount()
        {
            return _rulesConfig.Rules.Count(r => r.Enabled);
        }
    }

    /// <summary>
    /// 告警触发事件参数
    /// </summary>
    public class AlertTriggeredEventArgs : EventArgs
    {
        public MonitorRule Rule { get; set; } = null!;
        public FileAccessLog Log { get; set; } = null!;
        public DateTime AlertTime { get; set; }
        public string Message { get; set; } = string.Empty;
    }
}
