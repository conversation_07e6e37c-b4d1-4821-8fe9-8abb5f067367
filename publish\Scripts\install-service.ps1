# 文件监控服务安装脚本
# 需要以管理员权限运行

param(
    [string]$ServiceName = "FileMonitorService",
    [string]$DisplayName = "文件监控服务",
    [string]$Description = "监控文件服务器访问的Windows服务",
    [string]$BinaryPath = "",
    [string]$StartupType = "Automatic"
)

# 检查管理员权限
if (-NOT ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole] "Administrator")) {
    Write-Error "此脚本需要管理员权限运行。请以管理员身份运行PowerShell。"
    exit 1
}

# 设置默认二进制路径
if ([string]::IsNullOrEmpty($BinaryPath)) {
    $ScriptDir = Split-Path -Parent $MyInvocation.MyCommand.Definition
    $ProjectRoot = Split-Path -Parent $ScriptDir
    $BinaryPath = Join-Path $ProjectRoot "FileMonitor.Service\bin\Release\net8.0\FileMonitor.Service.exe"
}

Write-Host "=== 文件监控服务安装脚本 ===" -ForegroundColor Green
Write-Host "服务名称: $ServiceName"
Write-Host "显示名称: $DisplayName"
Write-Host "二进制路径: $BinaryPath"
Write-Host "启动类型: $StartupType"
Write-Host ""

# 检查二进制文件是否存在
if (-not (Test-Path $BinaryPath)) {
    Write-Error "找不到服务可执行文件: $BinaryPath"
    Write-Host "请先编译项目: dotnet publish -c Release" -ForegroundColor Yellow
    exit 1
}

try {
    # 检查服务是否已存在
    $existingService = Get-Service -Name $ServiceName -ErrorAction SilentlyContinue
    
    if ($existingService) {
        Write-Host "服务已存在，正在停止并删除..." -ForegroundColor Yellow
        
        # 停止服务
        if ($existingService.Status -eq 'Running') {
            Stop-Service -Name $ServiceName -Force
            Write-Host "服务已停止"
        }
        
        # 删除服务
        sc.exe delete $ServiceName
        Write-Host "旧服务已删除"
        
        # 等待服务完全删除
        Start-Sleep -Seconds 3
    }
    
    # 创建新服务
    Write-Host "正在创建服务..." -ForegroundColor Green
    
    $result = sc.exe create $ServiceName binPath= "`"$BinaryPath`"" DisplayName= $DisplayName start= auto
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host "服务创建成功" -ForegroundColor Green
        
        # 设置服务描述
        sc.exe description $ServiceName $Description
        
        # 设置服务恢复选项（失败时自动重启）
        sc.exe failure $ServiceName reset= 86400 actions= restart/60000/restart/60000/restart/60000
        
        Write-Host "服务配置完成" -ForegroundColor Green
        
        # 询问是否立即启动服务
        $startNow = Read-Host "是否立即启动服务? (Y/N)"
        if ($startNow -eq 'Y' -or $startNow -eq 'y') {
            Start-Service -Name $ServiceName
            Write-Host "服务已启动" -ForegroundColor Green
            
            # 显示服务状态
            Get-Service -Name $ServiceName | Format-Table -AutoSize
        }
        
    } else {
        Write-Error "服务创建失败，错误代码: $LASTEXITCODE"
        exit 1
    }
    
} catch {
    Write-Error "安装过程中发生错误: $($_.Exception.Message)"
    exit 1
}

Write-Host ""
Write-Host "=== 安装完成 ===" -ForegroundColor Green
Write-Host "服务管理命令:"
Write-Host "  启动服务: Start-Service -Name $ServiceName"
Write-Host "  停止服务: Stop-Service -Name $ServiceName"
Write-Host "  查看状态: Get-Service -Name $ServiceName"
Write-Host "  查看日志: Get-EventLog -LogName Application -Source $ServiceName -Newest 10"
Write-Host ""
Write-Host "配置文件位置:"
Write-Host "  主配置: $(Join-Path (Split-Path -Parent $BinaryPath) 'appsettings.json')"
Write-Host "  规则配置: $(Join-Path (Split-Path -Parent (Split-Path -Parent $BinaryPath)) 'Rules\rules.yaml')"
