﻿using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Configuration;
using FileMonitor.Core.Configuration;
using FileMonitor.Core.Database;
using FileMonitor.Core.Monitoring;
using FileMonitor.Core.Rules;
using FileMonitor.Core.Alerts;
using FileMonitor.Service.Services;

namespace FileMonitor.Service
{
    public class Program
    {
        public static async Task Main(string[] args)
        {
            var builder = Host.CreateApplicationBuilder(args);

            // 配置服务
            ConfigureServices(builder.Services, builder.Configuration);

            // 配置为 Windows 服务
            builder.Services.AddWindowsService(options =>
            {
                options.ServiceName = "FileMonitorService";
            });

            var host = builder.Build();

            // 运行服务
            await host.RunAsync();
        }

        private static void ConfigureServices(IServiceCollection services, IConfiguration configuration)
        {
            // 配置日志
            services.AddLogging(builder =>
            {
                builder.AddConsole();
                builder.AddEventLog();
                builder.AddDebug();
            });

            // 读取配置
            var appConfig = new AppConfig();
            configuration.Bind(appConfig);
            services.AddSingleton(appConfig);
            services.AddSingleton(appConfig.Email);
            services.AddSingleton(appConfig.Performance);

            // 注册核心服务
            services.AddSingleton<DatabaseService>(provider =>
                new DatabaseService(appConfig.ConnectionString));

            services.AddSingleton<FileSystemMonitor>();
            services.AddSingleton<EventLogMonitor>();
            services.AddSingleton<RuleEngine>();
            services.AddSingleton<AlertService>();

            // 注册主服务
            services.AddHostedService<FileMonitorWorkerService>();
        }
    }
}
