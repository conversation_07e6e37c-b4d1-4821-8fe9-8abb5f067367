using Microsoft.AspNetCore.Mvc;
using FileMonitor.Web.Services;
using FileMonitor.Web.Models;

namespace FileMonitor.Web.Controllers
{
    public class LogsController : Controller
    {
        private readonly LogService _logService;
        private readonly ILogger<LogsController> _logger;

        public LogsController(LogService logService, ILogger<LogsController> logger)
        {
            _logService = logService;
            _logger = logger;
        }

        public async Task<IActionResult> Index(LogFilterViewModel filter)
        {
            try
            {
                var model = await _logService.GetLogsAsync(filter);
                return View(model);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取日志数据时发生错误");
                return View(new LogListViewModel());
            }
        }

        [HttpGet]
        public async Task<IActionResult> Details(int id)
        {
            try
            {
                var log = await _logService.GetLogByIdAsync(id);
                if (log == null)
                {
                    return NotFound();
                }
                return View(log);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取日志详情时发生错误，ID: {Id}", id);
                return NotFound();
            }
        }

        [HttpGet]
        public async Task<IActionResult> Export(LogFilterViewModel filter)
        {
            try
            {
                var data = await _logService.ExportLogsAsync(filter);
                return File(data, "text/csv", $"access_logs_{DateTime.Now:yyyyMMdd_HHmmss}.csv");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "导出日志时发生错误");
                return BadRequest("导出失败");
            }
        }

        [HttpPost]
        public async Task<IActionResult> Search([FromBody] LogSearchRequest request)
        {
            try
            {
                var results = await _logService.SearchLogsAsync(request);
                return Json(results);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "搜索日志时发生错误");
                return Json(new { error = "搜索失败" });
            }
        }
    }
}
