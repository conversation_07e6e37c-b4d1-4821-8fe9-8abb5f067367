using FileMonitor.Core.Database;
using FileMonitor.Web.Models;
using Dapper;
using MySql.Data.MySqlClient;

namespace FileMonitor.Web.Services
{
    public class DashboardService
    {
        private readonly DatabaseService _databaseService;
        private readonly string _connectionString;

        public DashboardService(DatabaseService databaseService)
        {
            _databaseService = databaseService;
            _connectionString = databaseService.GetType()
                .GetField("_connectionString", System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance)?
                .GetValue(databaseService) as string ?? "";
        }

        public async Task<DashboardViewModel> GetDashboardDataAsync()
        {
            using var connection = new MySqlConnection(_connectionString);
            
            var model = new DashboardViewModel();

            // 获取今日统计
            var todayStats = await GetTodayStatisticsAsync(connection);
            model.TodayEvents = todayStats.TotalEvents;
            model.TodayAlerts = todayStats.TotalAlerts;
            model.UniqueUsers = todayStats.UniqueUsers;
            model.UniqueFiles = todayStats.UniqueFiles;

            // 获取最近7天的事件趋势
            model.EventTrend = await GetEventTrendAsync(connection, 7);

            // 获取最近的告警
            model.RecentAlerts = await GetRecentAlertsAsync(connection, 10);

            // 获取最活跃的用户
            model.TopUsers = await GetTopUsersAsync(connection, 10);

            // 获取最常访问的文件
            model.TopFiles = await GetTopFilesAsync(connection, 10);

            // 获取系统状态
            model.SystemStatus = await GetSystemStatusAsync();

            return model;
        }

        private async Task<(int TotalEvents, int TotalAlerts, int UniqueUsers, int UniqueFiles)> GetTodayStatisticsAsync(MySqlConnection connection)
        {
            var sql = @"
                SELECT 
                    COUNT(*) as TotalEvents,
                    COUNT(DISTINCT user_name) as UniqueUsers,
                    COUNT(DISTINCT file_path) as UniqueFiles
                FROM access_log 
                WHERE DATE(event_time) = CURDATE()";

            var stats = await connection.QuerySingleAsync(sql);

            var alertSql = @"
                SELECT COUNT(*) 
                FROM alert_history 
                WHERE DATE(alert_time) = CURDATE()";

            var alertCount = await connection.QuerySingleAsync<int>(alertSql);

            return (
                TotalEvents: (int)stats.TotalEvents,
                TotalAlerts: alertCount,
                UniqueUsers: (int)stats.UniqueUsers,
                UniqueFiles: (int)stats.UniqueFiles
            );
        }

        private async Task<List<EventTrendItem>> GetEventTrendAsync(MySqlConnection connection, int days)
        {
            var sql = @"
                SELECT 
                    DATE(event_time) as Date,
                    COUNT(*) as Count
                FROM access_log 
                WHERE event_time >= DATE_SUB(CURDATE(), INTERVAL @Days DAY)
                GROUP BY DATE(event_time)
                ORDER BY Date";

            var results = await connection.QueryAsync(sql, new { Days = days });
            
            return results.Select(r => new EventTrendItem
            {
                Date = (DateTime)r.Date,
                Count = (int)r.Count
            }).ToList();
        }

        private async Task<List<RecentAlertItem>> GetRecentAlertsAsync(MySqlConnection connection, int limit)
        {
            var sql = @"
                SELECT 
                    ah.id,
                    ah.rule_name,
                    ah.alert_time,
                    ah.alert_message,
                    ah.is_processed,
                    al.file_path,
                    al.user_name
                FROM alert_history ah
                LEFT JOIN access_log al ON ah.log_id = al.id
                ORDER BY ah.alert_time DESC
                LIMIT @Limit";

            var results = await connection.QueryAsync(sql, new { Limit = limit });
            
            return results.Select(r => new RecentAlertItem
            {
                Id = (int)r.id,
                RuleName = r.rule_name?.ToString() ?? "",
                AlertTime = (DateTime)r.alert_time,
                Message = r.alert_message?.ToString() ?? "",
                IsProcessed = (bool)r.is_processed,
                FilePath = r.file_path?.ToString() ?? "",
                UserName = r.user_name?.ToString() ?? ""
            }).ToList();
        }

        private async Task<List<TopUserItem>> GetTopUsersAsync(MySqlConnection connection, int limit)
        {
            var sql = @"
                SELECT 
                    user_name,
                    COUNT(*) as EventCount
                FROM access_log 
                WHERE event_time >= DATE_SUB(NOW(), INTERVAL 7 DAY)
                GROUP BY user_name
                ORDER BY EventCount DESC
                LIMIT @Limit";

            var results = await connection.QueryAsync(sql, new { Limit = limit });
            
            return results.Select(r => new TopUserItem
            {
                UserName = r.user_name?.ToString() ?? "",
                EventCount = (int)r.EventCount
            }).ToList();
        }

        private async Task<List<TopFileItem>> GetTopFilesAsync(MySqlConnection connection, int limit)
        {
            var sql = @"
                SELECT 
                    file_path,
                    COUNT(*) as AccessCount
                FROM access_log 
                WHERE event_time >= DATE_SUB(NOW(), INTERVAL 7 DAY)
                GROUP BY file_path
                ORDER BY AccessCount DESC
                LIMIT @Limit";

            var results = await connection.QueryAsync(sql, new { Limit = limit });
            
            return results.Select(r => new TopFileItem
            {
                FilePath = r.file_path?.ToString() ?? "",
                AccessCount = (int)r.AccessCount
            }).ToList();
        }

        private async Task<SystemStatusItem> GetSystemStatusAsync()
        {
            try
            {
                var isDbHealthy = await _databaseService.TestConnectionAsync();
                
                return new SystemStatusItem
                {
                    DatabaseStatus = isDbHealthy ? "正常" : "异常",
                    ServiceStatus = "运行中", // 这里可以实际检查服务状态
                    LastUpdateTime = DateTime.Now
                };
            }
            catch
            {
                return new SystemStatusItem
                {
                    DatabaseStatus = "异常",
                    ServiceStatus = "未知",
                    LastUpdateTime = DateTime.Now
                };
            }
        }
    }
}
