# 文件监控服务卸载脚本
# 需要以管理员权限运行

param(
    [string]$ServiceName = "FileMonitorService"
)

# 检查管理员权限
if (-NOT ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).<PERSON>In<PERSON><PERSON>([Security.Principal.WindowsBuiltInRole] "Administrator")) {
    Write-Error "此脚本需要管理员权限运行。请以管理员身份运行PowerShell。"
    exit 1
}

Write-Host "=== 文件监控服务卸载脚本 ===" -ForegroundColor Red
Write-Host "服务名称: $ServiceName"
Write-Host ""

try {
    # 检查服务是否存在
    $service = Get-Service -Name $ServiceName -ErrorAction SilentlyContinue
    
    if (-not $service) {
        Write-Host "服务 '$ServiceName' 不存在" -ForegroundColor Yellow
        exit 0
    }
    
    Write-Host "找到服务: $($service.DisplayName)" -ForegroundColor Green
    Write-Host "当前状态: $($service.Status)"
    
    # 确认卸载
    $confirm = Read-Host "确定要卸载此服务吗? (Y/N)"
    if ($confirm -ne 'Y' -and $confirm -ne 'y') {
        Write-Host "操作已取消" -ForegroundColor Yellow
        exit 0
    }
    
    # 停止服务
    if ($service.Status -eq 'Running') {
        Write-Host "正在停止服务..." -ForegroundColor Yellow
        Stop-Service -Name $ServiceName -Force -ErrorAction SilentlyContinue
        
        # 等待服务停止
        $timeout = 30
        $elapsed = 0
        do {
            Start-Sleep -Seconds 1
            $elapsed++
            $service = Get-Service -Name $ServiceName -ErrorAction SilentlyContinue
        } while ($service.Status -eq 'Running' -and $elapsed -lt $timeout)
        
        if ($service.Status -eq 'Running') {
            Write-Warning "服务未能在 $timeout 秒内停止，强制继续卸载"
        } else {
            Write-Host "服务已停止" -ForegroundColor Green
        }
    }
    
    # 删除服务
    Write-Host "正在删除服务..." -ForegroundColor Yellow
    $result = sc.exe delete $ServiceName
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host "服务删除成功" -ForegroundColor Green
    } else {
        Write-Error "服务删除失败，错误代码: $LASTEXITCODE"
        Write-Host "输出: $result"
        exit 1
    }
    
    # 等待服务完全删除
    Write-Host "等待服务完全删除..." -ForegroundColor Yellow
    Start-Sleep -Seconds 3
    
    # 验证删除
    $service = Get-Service -Name $ServiceName -ErrorAction SilentlyContinue
    if ($service) {
        Write-Warning "服务可能未完全删除，请重启计算机后再次检查"
    } else {
        Write-Host "服务已完全删除" -ForegroundColor Green
    }
    
} catch {
    Write-Error "卸载过程中发生错误: $($_.Exception.Message)"
    exit 1
}

Write-Host ""
Write-Host "=== 卸载完成 ===" -ForegroundColor Green
Write-Host "如果需要清理相关文件，请手动删除以下目录:"
Write-Host "  - 程序文件目录"
Write-Host "  - 日志文件目录"
Write-Host "  - 配置文件目录"
