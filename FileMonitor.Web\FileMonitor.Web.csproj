<Project Sdk="Microsoft.NET.Sdk.Web">

  <ItemGroup>
    <ProjectReference Include="..\FileMonitor.Core\FileMonitor.Core.csproj" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.AspNetCore.Mvc.Razor.RuntimeCompilation" Version="9.0.7" />
    <PackageReference Include="System.ServiceProcess.ServiceController" Version="9.0.7" />
  </ItemGroup>

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>

    <!-- 应用程序信息 -->
    <AssemblyTitle>文件监控系统 Web UI</AssemblyTitle>
    <AssemblyDescription>文件监控系统的Web管理界面</AssemblyDescription>
    <AssemblyCompany>文件监控系统</AssemblyCompany>
    <AssemblyProduct>FileMonitor System Web UI</AssemblyProduct>
    <AssemblyCopyright>Copyright © 2025</AssemblyCopyright>
    <AssemblyVersion>*******</AssemblyVersion>
    <FileVersion>*******</FileVersion>
    <ProductVersion>1.0.0</ProductVersion>

    <!-- 发布设置 -->
    <PublishSingleFile>false</PublishSingleFile>
    <SelfContained>false</SelfContained>
    <PublishReadyToRun>true</PublishReadyToRun>
  </PropertyGroup>

</Project>
