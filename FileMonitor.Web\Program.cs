using FileMonitor.Core.Configuration;
using FileMonitor.Core.Database;
using FileMonitor.Web.Services;

var builder = WebApplication.CreateBuilder(args);

// 添加服务
builder.Services.AddControllersWithViews()
    .AddRazorRuntimeCompilation();

// 读取配置
var appConfig = new AppConfig();
builder.Configuration.Bind(appConfig);
builder.Services.AddSingleton(appConfig);

// 注册数据库服务
builder.Services.AddSingleton<DatabaseService>(provider =>
    new DatabaseService(appConfig.ConnectionString));

// 注册业务服务
builder.Services.AddScoped<DashboardService>();
builder.Services.AddScoped<LogService>();
builder.Services.AddScoped<AlertService>();
builder.Services.AddScoped<SystemService>();

var app = builder.Build();

// 配置请求管道
if (!app.Environment.IsDevelopment())
{
    app.UseExceptionHandler("/Home/Error");
    app.UseHsts();
}

app.UseHttpsRedirection();
app.UseStaticFiles();
app.UseRouting();

app.MapControllerRoute(
    name: "default",
    pattern: "{controller=Home}/{action=Index}/{id?}");

app.Run();
