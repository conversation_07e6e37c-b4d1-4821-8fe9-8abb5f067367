using Microsoft.AspNetCore.Mvc;
using FileMonitor.Web.Services;
using FileMonitor.Web.Models;
using System.Diagnostics;

namespace FileMonitor.Web.Controllers
{
    public class HomeController : Controller
    {
        private readonly DashboardService _dashboardService;
        private readonly ILogger<HomeController> _logger;

        public HomeController(DashboardService dashboardService, ILogger<HomeController> logger)
        {
            _dashboardService = dashboardService;
            _logger = logger;
        }

        public async Task<IActionResult> Index()
        {
            try
            {
                var model = await _dashboardService.GetDashboardDataAsync();
                return View(model);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取仪表板数据时发生错误");
                return View(new DashboardViewModel());
            }
        }

        public IActionResult Privacy()
        {
            return View();
        }

        [ResponseCache(Duration = 0, Location = ResponseCacheLocation.None, NoStore = true)]
        public IActionResult Error()
        {
            return View(new ErrorViewModel { RequestId = Activity.Current?.Id ?? HttpContext.TraceIdentifier });
        }
    }
}
