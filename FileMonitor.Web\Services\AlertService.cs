using FileMonitor.Core.Database;
using FileMonitor.Core.Models;
using FileMonitor.Web.Models;
using Dapper;
using MySql.Data.MySqlClient;

namespace FileMonitor.Web.Services
{
    public class AlertService
    {
        private readonly string _connectionString;

        public AlertService(DatabaseService databaseService)
        {
            _connectionString = databaseService.GetType()
                .GetField("_connectionString", System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance)?
                .GetValue(databaseService) as string ?? "";
        }

        public async Task<AlertListViewModel> GetAlertsAsync(AlertFilterViewModel filter)
        {
            using var connection = new MySqlConnection(_connectionString);

            var whereClause = BuildWhereClause(filter);
            var parameters = BuildParameters(filter);

            // 获取总数
            var countSql = $"SELECT COUNT(*) FROM alert_history {whereClause}";
            var totalCount = await connection.QuerySingleAsync<int>(countSql, parameters);

            // 获取分页数据
            var offset = (filter.Page - 1) * filter.PageSize;
            var dataSql = $@"
                SELECT * FROM alert_history 
                {whereClause}
                ORDER BY alert_time DESC 
                LIMIT @PageSize OFFSET @Offset";

            parameters.Add("PageSize", filter.PageSize);
            parameters.Add("Offset", offset);

            var alerts = await connection.QueryAsync<AlertHistory>(dataSql, parameters);

            return new AlertListViewModel
            {
                Alerts = alerts.ToList(),
                Filter = filter,
                TotalCount = totalCount,
                TotalPages = (int)Math.Ceiling((double)totalCount / filter.PageSize)
            };
        }

        public async Task<AlertHistory?> GetAlertByIdAsync(int id)
        {
            using var connection = new MySqlConnection(_connectionString);
            
            var sql = "SELECT * FROM alert_history WHERE id = @Id";
            return await connection.QuerySingleOrDefaultAsync<AlertHistory>(sql, new { Id = id });
        }

        public async Task MarkAsProcessedAsync(int id, string processedBy)
        {
            using var connection = new MySqlConnection(_connectionString);
            
            var sql = @"
                UPDATE alert_history 
                SET is_processed = true, 
                    processed_by = @ProcessedBy, 
                    processed_at = NOW() 
                WHERE id = @Id";

            await connection.ExecuteAsync(sql, new { Id = id, ProcessedBy = processedBy });
        }

        public async Task<AlertStatistics> GetAlertStatisticsAsync()
        {
            using var connection = new MySqlConnection(_connectionString);

            // 获取基本统计
            var basicStatsSql = @"
                SELECT 
                    COUNT(*) as TotalAlerts,
                    SUM(CASE WHEN is_processed = true THEN 1 ELSE 0 END) as ProcessedAlerts,
                    SUM(CASE WHEN is_processed = false THEN 1 ELSE 0 END) as UnprocessedAlerts
                FROM alert_history";

            var basicStats = await connection.QuerySingleAsync(basicStatsSql);

            // 获取按规则分组的统计
            var ruleStatsSql = @"
                SELECT 
                    rule_name as RuleName,
                    COUNT(*) as Count
                FROM alert_history 
                WHERE alert_time >= DATE_SUB(NOW(), INTERVAL 30 DAY)
                GROUP BY rule_name
                ORDER BY Count DESC
                LIMIT 10";

            var ruleStats = await connection.QueryAsync(ruleStatsSql);

            // 获取趋势数据
            var trendSql = @"
                SELECT 
                    DATE(alert_time) as Date,
                    COUNT(*) as Count
                FROM alert_history 
                WHERE alert_time >= DATE_SUB(NOW(), INTERVAL 7 DAY)
                GROUP BY DATE(alert_time)
                ORDER BY Date";

            var trendData = await connection.QueryAsync(trendSql);

            return new AlertStatistics
            {
                TotalAlerts = (int)basicStats.TotalAlerts,
                ProcessedAlerts = (int)basicStats.ProcessedAlerts,
                UnprocessedAlerts = (int)basicStats.UnprocessedAlerts,
                AlertsByRule = ruleStats.Select(r => new AlertByRuleItem
                {
                    RuleName = r.RuleName?.ToString() ?? "",
                    Count = (int)r.Count
                }).ToList(),
                AlertTrend = trendData.Select(t => new AlertTrendItem
                {
                    Date = (DateTime)t.Date,
                    Count = (int)t.Count
                }).ToList()
            };
        }

        private string BuildWhereClause(AlertFilterViewModel filter)
        {
            var conditions = new List<string>();

            if (!string.IsNullOrEmpty(filter.RuleName))
                conditions.Add("rule_name LIKE @RuleName");

            if (filter.IsProcessed.HasValue)
                conditions.Add("is_processed = @IsProcessed");

            if (filter.StartDate.HasValue)
                conditions.Add("alert_time >= @StartDate");

            if (filter.EndDate.HasValue)
                conditions.Add("alert_time <= @EndDate");

            return conditions.Any() ? "WHERE " + string.Join(" AND ", conditions) : "";
        }

        private DynamicParameters BuildParameters(AlertFilterViewModel filter)
        {
            var parameters = new DynamicParameters();

            if (!string.IsNullOrEmpty(filter.RuleName))
                parameters.Add("RuleName", $"%{filter.RuleName}%");

            if (filter.IsProcessed.HasValue)
                parameters.Add("IsProcessed", filter.IsProcessed.Value);

            if (filter.StartDate.HasValue)
                parameters.Add("StartDate", filter.StartDate.Value);

            if (filter.EndDate.HasValue)
                parameters.Add("EndDate", filter.EndDate.Value);

            return parameters;
        }
    }
}
