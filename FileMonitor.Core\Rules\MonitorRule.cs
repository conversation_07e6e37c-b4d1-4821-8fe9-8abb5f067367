using System;
using System.Collections.Generic;
using System.Text.RegularExpressions;

namespace FileMonitor.Core.Rules
{
    /// <summary>
    /// 监控规则类
    /// </summary>
    public class MonitorRule
    {
        /// <summary>
        /// 规则名称
        /// </summary>
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// 规则描述
        /// </summary>
        public string Description { get; set; } = string.Empty;

        /// <summary>
        /// 是否启用
        /// </summary>
        public bool Enabled { get; set; } = true;

        /// <summary>
        /// 告警级别
        /// </summary>
        public AlertLevel Level { get; set; } = AlertLevel.Medium;

        /// <summary>
        /// 匹配条件
        /// </summary>
        public RuleConditions Conditions { get; set; } = new RuleConditions();

        /// <summary>
        /// 告警配置
        /// </summary>
        public AlertConfig Alert { get; set; } = new AlertConfig();
    }

    /// <summary>
    /// 规则条件类
    /// </summary>
    public class RuleConditions
    {
        /// <summary>
        /// 文件路径匹配模式（正则表达式）
        /// </summary>
        public string? PathPattern { get; set; }

        /// <summary>
        /// 用户名匹配模式（正则表达式）
        /// </summary>
        public string? UserPattern { get; set; }

        /// <summary>
        /// 操作类型过滤器
        /// </summary>
        public List<string> Actions { get; set; } = new List<string>();

        /// <summary>
        /// 时间范围过滤器（格式：HH:mm-HH:mm）
        /// </summary>
        public string? TimeRange { get; set; }

        /// <summary>
        /// IP地址匹配模式（正则表达式）
        /// </summary>
        public string? IpPattern { get; set; }

        /// <summary>
        /// 文件扩展名过滤器
        /// </summary>
        public List<string> FileExtensions { get; set; } = new List<string>();

        /// <summary>
        /// 最小文件大小（字节）
        /// </summary>
        public long? MinFileSize { get; set; }

        /// <summary>
        /// 最大文件大小（字节）
        /// </summary>
        public long? MaxFileSize { get; set; }
    }

    /// <summary>
    /// 告警配置类
    /// </summary>
    public class AlertConfig
    {
        /// <summary>
        /// 告警方式
        /// </summary>
        public List<string> Methods { get; set; } = new List<string>();

        /// <summary>
        /// 邮件接收者
        /// </summary>
        public List<string> Recipients { get; set; } = new List<string>();

        /// <summary>
        /// 告警消息模板
        /// </summary>
        public string MessageTemplate { get; set; } = "检测到文件访问: {FilePath} 被用户 {UserName} 执行了 {Action} 操作";

        /// <summary>
        /// 告警频率限制（秒）
        /// </summary>
        public int ThrottleSeconds { get; set; } = 300;
    }

    /// <summary>
    /// 告警级别枚举
    /// </summary>
    public enum AlertLevel
    {
        Low,
        Medium,
        High,
        Critical
    }

    /// <summary>
    /// 规则配置根对象
    /// </summary>
    public class RulesConfig
    {
        /// <summary>
        /// 规则列表
        /// </summary>
        public List<MonitorRule> Rules { get; set; } = new List<MonitorRule>();

        /// <summary>
        /// 全局设置
        /// </summary>
        public GlobalSettings Global { get; set; } = new GlobalSettings();
    }

    /// <summary>
    /// 全局设置类
    /// </summary>
    public class GlobalSettings
    {
        /// <summary>
        /// 默认告警级别
        /// </summary>
        public AlertLevel DefaultAlertLevel { get; set; } = AlertLevel.Medium;

        /// <summary>
        /// 是否启用所有规则
        /// </summary>
        public bool EnableAllRules { get; set; } = true;

        /// <summary>
        /// 日志保留天数
        /// </summary>
        public int LogRetentionDays { get; set; } = 90;

        /// <summary>
        /// 最大告警频率（每分钟）
        /// </summary>
        public int MaxAlertsPerMinute { get; set; } = 10;
    }
}
