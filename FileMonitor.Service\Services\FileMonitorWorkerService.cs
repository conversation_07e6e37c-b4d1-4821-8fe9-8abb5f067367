using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using FileMonitor.Core.Configuration;
using FileMonitor.Core.Database;
using FileMonitor.Core.Monitoring;
using FileMonitor.Core.Rules;
using FileMonitor.Core.Alerts;
using System;
using System.Threading;
using System.Threading.Tasks;

namespace FileMonitor.Service.Services
{
    /// <summary>
    /// 文件监控 Worker 服务
    /// </summary>
    public class FileMonitorWorkerService : BackgroundService
    {
        private readonly ILogger<FileMonitorWorkerService> _logger;
        private readonly AppConfig _appConfig;
        private readonly DatabaseService _databaseService;
        private readonly FileSystemMonitor _fileSystemMonitor;
        private readonly EventLogMonitor _eventLogMonitor;
        private readonly RuleEngine _ruleEngine;
        private readonly AlertService _alertService;
        private readonly Timer _batchFlushTimer;
        private readonly Timer _cleanupTimer;

        public FileMonitorWorkerService(
            ILogger<FileMonitorWorkerService> logger,
            AppConfig appConfig,
            DatabaseService databaseService,
            FileSystemMonitor fileSystemMonitor,
            EventLogMonitor eventLogMonitor,
            RuleEngine ruleEngine,
            AlertService alertService)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _appConfig = appConfig ?? throw new ArgumentNullException(nameof(appConfig));
            _databaseService = databaseService ?? throw new ArgumentNullException(nameof(databaseService));
            _fileSystemMonitor = fileSystemMonitor ?? throw new ArgumentNullException(nameof(fileSystemMonitor));
            _eventLogMonitor = eventLogMonitor ?? throw new ArgumentNullException(nameof(eventLogMonitor));
            _ruleEngine = ruleEngine ?? throw new ArgumentNullException(nameof(ruleEngine));
            _alertService = alertService ?? throw new ArgumentNullException(nameof(alertService));

            // 设置定时器
            _batchFlushTimer = new Timer(BatchFlushCallback, null, Timeout.Infinite, Timeout.Infinite);
            _cleanupTimer = new Timer(CleanupCallback, null, Timeout.Infinite, Timeout.Infinite);
        }

        protected override async Task ExecuteAsync(CancellationToken stoppingToken)
        {
            _logger.LogInformation("文件监控服务正在启动...");

            try
            {
                // 初始化数据库
                await InitializeDatabaseAsync();

                // 加载规则
                await LoadRulesAsync();

                // 设置事件处理
                SetupEventHandlers();

                // 启动监控
                await StartMonitoringAsync();

                // 启动定时任务
                StartTimers();

                _logger.LogInformation("文件监控服务已成功启动");

                // 保持服务运行
                while (!stoppingToken.IsCancellationRequested)
                {
                    await Task.Delay(5000, stoppingToken);
                    
                    // 定期检查服务状态
                    await CheckServiceHealthAsync();
                }
            }
            catch (Exception ex)
            {
                _logger.LogCritical(ex, "文件监控服务启动失败");
                throw;
            }
        }

        /// <summary>
        /// 初始化数据库
        /// </summary>
        private async Task InitializeDatabaseAsync()
        {
            try
            {
                _logger.LogInformation("正在初始化数据库...");
                
                // 测试数据库连接
                var isConnected = await _databaseService.TestConnectionAsync();
                if (!isConnected)
                {
                    throw new InvalidOperationException("无法连接到数据库");
                }

                // 初始化数据库表结构
                await _databaseService.InitializeDatabaseAsync();
                
                _logger.LogInformation("数据库初始化完成");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "数据库初始化失败");
                throw;
            }
        }

        /// <summary>
        /// 加载规则配置
        /// </summary>
        private async Task LoadRulesAsync()
        {
            try
            {
                _logger.LogInformation("正在加载规则配置...");
                
                await _ruleEngine.LoadRulesFromFileAsync(_appConfig.RulesConfigPath);
                
                _logger.LogInformation("已加载 {EnabledRules}/{TotalRules} 条规则", 
                    _ruleEngine.GetEnabledRuleCount(), 
                    _ruleEngine.GetRuleCount());
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "加载规则配置失败");
                throw;
            }
        }

        /// <summary>
        /// 设置事件处理器
        /// </summary>
        private void SetupEventHandlers()
        {
            // 文件系统监控事件
            _fileSystemMonitor.FileAccessDetected += async (sender, log) =>
            {
                try
                {
                    await _ruleEngine.EvaluateLogAsync(log);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "处理文件系统事件时发生错误");
                }
            };

            // 事件日志监控事件
            _eventLogMonitor.FileAccessDetected += async (sender, log) =>
            {
                try
                {
                    await _ruleEngine.EvaluateLogAsync(log);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "处理事件日志时发生错误");
                }
            };

            // 规则引擎告警事件
            _ruleEngine.AlertTriggered += async (sender, alertArgs) =>
            {
                try
                {
                    await _alertService.HandleAlertAsync(alertArgs);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "处理告警时发生错误");
                }
            };
        }

        /// <summary>
        /// 启动监控
        /// </summary>
        private async Task StartMonitoringAsync()
        {
            try
            {
                // 启动文件系统监控
                if (_appConfig.EnableFileSystemMonitoring)
                {
                    _fileSystemMonitor.StartMonitoring(_appConfig.MonitorPaths);
                    _logger.LogInformation("文件系统监控已启动，监控路径数: {Count}", _appConfig.MonitorPaths.Count);
                }

                // 启动事件日志监控
                if (_appConfig.EnableEventLogMonitoring)
                {
                    _eventLogMonitor.StartMonitoring();
                    _logger.LogInformation("Windows事件日志监控已启动");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "启动监控时发生错误");
                throw;
            }
        }

        /// <summary>
        /// 启动定时器
        /// </summary>
        private void StartTimers()
        {
            // 批量刷新定时器
            var batchInterval = TimeSpan.FromSeconds(_appConfig.Performance.BatchIntervalSeconds);
            _batchFlushTimer.Change(batchInterval, batchInterval);

            // 清理定时器（每小时执行一次）
            var cleanupInterval = TimeSpan.FromHours(1);
            _cleanupTimer.Change(cleanupInterval, cleanupInterval);

            _logger.LogInformation("定时任务已启动");
        }

        /// <summary>
        /// 批量刷新回调
        /// </summary>
        private async void BatchFlushCallback(object? state)
        {
            try
            {
                await _fileSystemMonitor.FlushBufferAsync();
                _logger.LogDebug("批量刷新完成");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "批量刷新时发生错误");
            }
        }

        /// <summary>
        /// 清理任务回调
        /// </summary>
        private void CleanupCallback(object? state)
        {
            try
            {
                _ruleEngine.CleanupThrottleCache();
                _logger.LogDebug("清理任务完成");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "清理任务时发生错误");
            }
        }

        /// <summary>
        /// 检查服务健康状态
        /// </summary>
        private async Task CheckServiceHealthAsync()
        {
            try
            {
                // 检查数据库连接
                var isDbHealthy = await _databaseService.TestConnectionAsync();
                if (!isDbHealthy)
                {
                    _logger.LogWarning("数据库连接异常");
                }

                // 检查缓冲区大小
                var bufferCount = _fileSystemMonitor.GetBufferCount();
                if (bufferCount > _appConfig.Performance.MaxCacheSize)
                {
                    _logger.LogWarning("文件监控缓冲区过大: {Count}", bufferCount);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "健康检查时发生错误");
            }
        }

        public override async Task StopAsync(CancellationToken cancellationToken)
        {
            _logger.LogInformation("文件监控服务正在停止...");

            try
            {
                // 停止定时器
                _batchFlushTimer?.Change(Timeout.Infinite, Timeout.Infinite);
                _cleanupTimer?.Change(Timeout.Infinite, Timeout.Infinite);

                // 停止监控
                _fileSystemMonitor?.StopMonitoring();
                _eventLogMonitor?.StopMonitoring();

                // 最后一次刷新缓冲区
                await _fileSystemMonitor.FlushBufferAsync();

                _logger.LogInformation("文件监控服务已停止");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "停止服务时发生错误");
            }

            await base.StopAsync(cancellationToken);
        }

        public override void Dispose()
        {
            _batchFlushTimer?.Dispose();
            _cleanupTimer?.Dispose();
            _fileSystemMonitor?.Dispose();
            _eventLogMonitor?.Dispose();
            base.Dispose();
        }
    }
}
