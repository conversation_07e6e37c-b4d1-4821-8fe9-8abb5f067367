<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>@ViewData["Title"] - 文件监控系统</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" />
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" />
    <link rel="stylesheet" href="~/css/site.css" asp-append-version="true" />
    <style>
        .sidebar {
            position: fixed;
            top: 0;
            bottom: 0;
            left: 0;
            z-index: 100;
            padding: 48px 0 0;
            box-shadow: inset -1px 0 0 rgba(0, 0, 0, .1);
        }
        .sidebar-sticky {
            position: relative;
            top: 0;
            height: calc(100vh - 48px);
            padding-top: .5rem;
            overflow-x: hidden;
            overflow-y: auto;
        }
        .navbar-brand {
            padding-top: .75rem;
            padding-bottom: .75rem;
        }
        main {
            margin-left: 240px;
        }
        .alert-badge {
            font-size: 0.75rem;
        }
    </style>
</head>
<body>
    <nav class="navbar navbar-dark sticky-top bg-dark flex-md-nowrap p-0 shadow">
        <a class="navbar-brand col-md-3 col-lg-2 me-0 px-3 fs-6" href="/">
            <i class="bi bi-shield-check"></i> 文件监控系统
        </a>
        <button class="navbar-toggler position-absolute d-md-none collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#sidebarMenu">
            <span class="navbar-toggler-icon"></span>
        </button>
        <div class="navbar-nav">
            <div class="nav-item text-nowrap">
                <a class="nav-link px-3" href="#" onclick="refreshData()">
                    <i class="bi bi-arrow-clockwise"></i> 刷新
                </a>
            </div>
        </div>
    </nav>

    <div class="container-fluid">
        <div class="row">
            <nav id="sidebarMenu" class="col-md-3 col-lg-2 d-md-block bg-light sidebar collapse">
                <div class="position-sticky pt-3 sidebar-sticky">
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link @(ViewContext.RouteData.Values["Controller"]?.ToString() == "Home" ? "active" : "")" href="/">
                                <i class="bi bi-house-door"></i> 仪表板
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link @(ViewContext.RouteData.Values["Controller"]?.ToString() == "Logs" ? "active" : "")" href="/Logs">
                                <i class="bi bi-file-text"></i> 访问日志
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link @(ViewContext.RouteData.Values["Controller"]?.ToString() == "Alerts" ? "active" : "")" href="/Alerts">
                                <i class="bi bi-exclamation-triangle"></i> 告警管理
                                <span class="badge bg-danger ms-2 alert-badge" id="unprocessed-alerts">0</span>
                            </a>
                        </li>
                    </ul>

                    <h6 class="sidebar-heading d-flex justify-content-between align-items-center px-3 mt-4 mb-1 text-muted text-uppercase">
                        <span>系统管理</span>
                    </h6>
                    <ul class="nav flex-column mb-2">
                        <li class="nav-item">
                            <a class="nav-link @(ViewContext.RouteData.Values["Controller"]?.ToString() == "System" && ViewContext.RouteData.Values["Action"]?.ToString() == "Index" ? "active" : "")" href="/System">
                                <i class="bi bi-gear"></i> 系统状态
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link @(ViewContext.RouteData.Values["Controller"]?.ToString() == "System" && ViewContext.RouteData.Values["Action"]?.ToString() == "Configuration" ? "active" : "")" href="/System/Configuration">
                                <i class="bi bi-sliders"></i> 系统配置
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link @(ViewContext.RouteData.Values["Controller"]?.ToString() == "System" && ViewContext.RouteData.Values["Action"]?.ToString() == "Rules" ? "active" : "")" href="/System/Rules">
                                <i class="bi bi-list-check"></i> 规则管理
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>

            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">@ViewData["Title"]</h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        @RenderSection("PageActions", required: false)
                    </div>
                </div>

                @if (TempData["Success"] != null)
                {
                    <div class="alert alert-success alert-dismissible fade show" role="alert">
                        @TempData["Success"]
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                }

                @if (TempData["Error"] != null)
                {
                    <div class="alert alert-danger alert-dismissible fade show" role="alert">
                        @TempData["Error"]
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                }

                @RenderBody()
            </main>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="~/js/site.js" asp-append-version="true"></script>
    
    <script>
        // 定期更新未处理告警数量
        function updateAlertBadge() {
            fetch('/Alerts/Statistics')
                .then(response => response.json())
                .then(data => {
                    if (data.UnprocessedAlerts !== undefined) {
                        const badge = document.getElementById('unprocessed-alerts');
                        badge.textContent = data.UnprocessedAlerts;
                        badge.style.display = data.UnprocessedAlerts > 0 ? 'inline' : 'none';
                    }
                })
                .catch(error => console.error('更新告警徽章失败:', error));
        }

        function refreshData() {
            location.reload();
        }

        // 页面加载完成后更新告警徽章
        document.addEventListener('DOMContentLoaded', function() {
            updateAlertBadge();
            // 每30秒更新一次
            setInterval(updateAlertBadge, 30000);
        });
    </script>

    @await RenderSectionAsync("Scripts", required: false)
</body>
</html>
