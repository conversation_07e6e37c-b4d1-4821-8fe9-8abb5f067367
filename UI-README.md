# 文件监控系统 Web UI

## 🎯 项目概述

文件监控系统 Web UI 是一个基于 ASP.NET Core 的现代化 Web 管理界面，为文件监控系统提供直观的可视化管理和监控功能。

## ✨ 主要功能

### 📊 仪表板 (Dashboard)
- **实时统计**: 今日事件数、告警数、活跃用户数、访问文件数
- **趋势图表**: 最近7天的事件趋势可视化
- **系统状态**: 数据库连接状态、服务运行状态实时监控
- **快速概览**: 最近告警、活跃用户、热门文件等关键信息

### 📋 访问日志管理 (Logs)
- **高级筛选**: 支持按文件路径、用户名、操作类型、时间范围筛选
- **实时搜索**: 快速搜索文件路径、用户名或操作类型
- **分页浏览**: 支持自定义每页显示数量 (20/50/100)
- **详情查看**: 点击查看单条日志的详细信息
- **数据导出**: 支持 CSV 格式导出筛选后的日志数据

### 🚨 告警管理 (Alerts)
- **告警列表**: 查看所有安全告警，支持按规则名称、处理状态筛选
- **状态管理**: 标记告警为已处理，记录处理人和处理时间
- **统计分析**: 告警趋势图表、按规则分组统计
- **实时更新**: 未处理告警数量实时更新显示

### ⚙️ 系统管理 (System)
- **系统状态**: 数据库连接、服务运行状态、性能指标监控
- **配置管理**: 在线修改系统配置参数
- **规则管理**: 查看和管理监控规则配置
- **服务控制**: 测试数据库连接、重启监控服务

## 🏗️ 技术架构

### 前端技术栈
- **Bootstrap 5.3**: 响应式 UI 框架
- **Chart.js**: 数据可视化图表库
- **Bootstrap Icons**: 图标库
- **原生 JavaScript**: 交互功能实现

### 后端技术栈
- **ASP.NET Core 8.0**: Web 框架
- **MySQL**: 数据存储
- **Dapper**: 轻量级 ORM
- **依赖注入**: 服务管理

### 项目结构
```
FileMonitor.Web/
├── Controllers/           # MVC 控制器
│   ├── HomeController.cs     # 仪表板控制器
│   ├── LogsController.cs     # 日志管理控制器
│   ├── AlertsController.cs   # 告警管理控制器
│   └── SystemController.cs   # 系统管理控制器
├── Services/             # 业务服务层
│   ├── DashboardService.cs   # 仪表板数据服务
│   ├── LogService.cs         # 日志数据服务
│   ├── AlertService.cs       # 告警数据服务
│   └── SystemService.cs      # 系统管理服务
├── Models/               # 视图模型
│   └── ViewModels.cs         # 所有视图模型定义
├── Views/                # Razor 视图
│   ├── Home/                 # 仪表板视图
│   ├── Logs/                 # 日志管理视图
│   ├── Alerts/               # 告警管理视图
│   ├── System/               # 系统管理视图
│   └── Shared/               # 共享视图和布局
└── wwwroot/              # 静态资源
    ├── css/                  # 样式文件
    └── js/                   # JavaScript 文件
```

## 🚀 快速开始

### 环境要求
- .NET 8.0 SDK
- MySQL 8.0+
- 现代浏览器 (Chrome, Firefox, Edge, Safari)

### 1. 编译项目
```bash
# 编译整个解决方案
dotnet build

# 仅编译 Web 项目
dotnet build FileMonitor.Web
```

### 2. 配置数据库
确保 `appsettings.json` 中的数据库连接字符串正确：
```json
{
  "ConnectionString": "Server=127.0.0.1;Database=FileMonitorDB;User=root;Password=******;"
}
```

### 3. 启动应用

#### 开发模式
```bash
dotnet run --project FileMonitor.Web
```

#### 使用启动脚本
```powershell
# Windows PowerShell
.\Scripts\start-web-ui.ps1

# 指定端口
.\Scripts\start-web-ui.ps1 -Port 8080
```

### 4. 访问界面
打开浏览器访问: `http://localhost:5000` (或指定的端口)

## 📦 部署

### 开发环境部署
```powershell
.\Scripts\deploy-web-ui.ps1
```

### 生产环境部署
```powershell
.\Scripts\deploy-web-ui.ps1 -Configuration Release -Port 80 -StartAfterDeploy
```

### IIS 部署
1. 发布应用到目标目录
2. 在 IIS 中创建新站点
3. 设置应用程序池为 .NET Core
4. 配置站点绑定和权限

## 🎨 界面预览

### 仪表板
- 📈 实时统计卡片显示关键指标
- 📊 事件趋势图表展示历史数据
- 🔍 系统状态实时监控
- 📋 最近告警和活跃用户列表

### 日志管理
- 🔍 强大的筛选和搜索功能
- 📄 分页浏览大量日志数据
- 📊 操作类型彩色标签区分
- 📥 一键导出筛选结果

### 告警管理
- 🚨 告警级别颜色区分 (Low/Medium/High/Critical)
- ✅ 一键标记告警处理状态
- 📈 告警统计图表分析
- 🔔 实时未处理告警提醒

### 系统管理
- 💾 系统状态实时监控
- ⚙️ 在线配置参数修改
- 🔧 服务控制和测试功能
- 📋 规则配置查看管理

## 🔧 配置说明

### 主要配置项
| 配置项 | 说明 | 默认值 |
|--------|------|--------|
| ConnectionString | MySQL 连接字符串 | - |
| MonitorPaths | 监控路径列表 | [] |
| EnableEventLogMonitoring | 启用事件日志监控 | true |
| EnableFileSystemMonitoring | 启用文件系统监控 | true |

### 性能配置
| 配置项 | 说明 | 默认值 |
|--------|------|--------|
| BatchSize | 批量处理大小 | 100 |
| BatchIntervalSeconds | 批量间隔秒数 | 30 |
| LogRetentionDays | 日志保留天数 | 90 |

## 🛠️ 开发指南

### 添加新功能
1. 在 `Controllers/` 中创建控制器
2. 在 `Services/` 中实现业务逻辑
3. 在 `Models/` 中定义视图模型
4. 在 `Views/` 中创建视图页面

### 自定义样式
- 修改 `wwwroot/css/site.css` 添加自定义样式
- 使用 Bootstrap 5 的工具类快速开发

### 添加 JavaScript 功能
- 在 `wwwroot/js/site.js` 中添加全局功能
- 在视图的 `@section Scripts` 中添加页面特定脚本

## 🔍 故障排除

### 常见问题

1. **应用无法启动**
   - 检查 .NET 8.0 运行时是否安装
   - 验证端口是否被占用
   - 查看控制台错误信息

2. **数据库连接失败**
   - 确认 MySQL 服务正在运行
   - 检查连接字符串配置
   - 验证数据库用户权限

3. **页面显示异常**
   - 清除浏览器缓存
   - 检查浏览器控制台错误
   - 验证静态资源是否正确加载

### 日志查看
- 应用日志: `logs/` 目录下的日志文件
- 开发日志: Visual Studio 输出窗口或控制台
- IIS 日志: IIS 管理器中的日志设置

## 📈 性能优化

- 使用 CDN 加载 Bootstrap 和 Chart.js
- 启用 Gzip 压缩
- 配置静态资源缓存
- 数据库查询优化和索引

## 🔒 安全考虑

- 输入验证和 XSS 防护
- CSRF 令牌保护
- HTTPS 强制重定向
- 敏感信息加密存储

---

**版本**: v1.0.0  
**构建时间**: 2025-07-18  
**技术支持**: 文件监控系统开发团队
