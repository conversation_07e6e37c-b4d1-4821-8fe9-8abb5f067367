@model FileMonitor.Web.Models.DashboardViewModel
@{
    ViewData["Title"] = "仪表板";
}

<!-- 统计卡片 -->
<div class="row mb-4">
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-primary shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">今日事件</div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">@Model.TodayEvents</div>
                    </div>
                    <div class="col-auto">
                        <i class="bi bi-file-text fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-warning shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">今日告警</div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">@Model.TodayAlerts</div>
                    </div>
                    <div class="col-auto">
                        <i class="bi bi-exclamation-triangle fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-info shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-info text-uppercase mb-1">活跃用户</div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">@Model.UniqueUsers</div>
                    </div>
                    <div class="col-auto">
                        <i class="bi bi-people fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-success shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-success text-uppercase mb-1">访问文件</div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">@Model.UniqueFiles</div>
                    </div>
                    <div class="col-auto">
                        <i class="bi bi-folder fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- 事件趋势图 -->
    <div class="col-xl-8 col-lg-7">
        <div class="card shadow mb-4">
            <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                <h6 class="m-0 font-weight-bold text-primary">事件趋势 (最近7天)</h6>
            </div>
            <div class="card-body">
                <div class="chart-area">
                    <canvas id="eventTrendChart"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- 系统状态 -->
    <div class="col-xl-4 col-lg-5">
        <div class="card shadow mb-4">
            <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                <h6 class="m-0 font-weight-bold text-primary">系统状态</h6>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <div class="small text-gray-500">数据库状态</div>
                    <div class="h5 mb-0">
                        <span class="badge bg-@(Model.SystemStatus.DatabaseStatus == "正常" ? "success" : "danger")">
                            @Model.SystemStatus.DatabaseStatus
                        </span>
                    </div>
                </div>
                <div class="mb-3">
                    <div class="small text-gray-500">服务状态</div>
                    <div class="h5 mb-0">
                        <span class="badge bg-@(Model.SystemStatus.ServiceStatus == "运行中" ? "success" : "warning")">
                            @Model.SystemStatus.ServiceStatus
                        </span>
                    </div>
                </div>
                <div class="mb-3">
                    <div class="small text-gray-500">最后更新</div>
                    <div class="h6 mb-0">@Model.SystemStatus.LastUpdateTime.ToString("yyyy-MM-dd HH:mm:ss")</div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- 最近告警 -->
    <div class="col-lg-6 mb-4">
        <div class="card shadow">
            <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                <h6 class="m-0 font-weight-bold text-primary">最近告警</h6>
                <a href="/Alerts" class="btn btn-sm btn-primary">查看全部</a>
            </div>
            <div class="card-body">
                @if (Model.RecentAlerts.Any())
                {
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>规则</th>
                                    <th>时间</th>
                                    <th>状态</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach (var alert in Model.RecentAlerts.Take(5))
                                {
                                    <tr>
                                        <td>
                                            <small>@alert.RuleName</small><br>
                                            <small class="text-muted">@alert.UserName</small>
                                        </td>
                                        <td>
                                            <small>@alert.AlertTime.ToString("MM-dd HH:mm")</small>
                                        </td>
                                        <td>
                                            <span class="badge bg-@(alert.IsProcessed ? "success" : "warning")">
                                                @(alert.IsProcessed ? "已处理" : "待处理")
                                            </span>
                                        </td>
                                    </tr>
                                }
                            </tbody>
                        </table>
                    </div>
                }
                else
                {
                    <p class="text-muted">暂无告警记录</p>
                }
            </div>
        </div>
    </div>

    <!-- 活跃用户 -->
    <div class="col-lg-6 mb-4">
        <div class="card shadow">
            <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                <h6 class="m-0 font-weight-bold text-primary">活跃用户 (最近7天)</h6>
            </div>
            <div class="card-body">
                @if (Model.TopUsers.Any())
                {
                    @foreach (var user in Model.TopUsers.Take(5))
                    {
                        <div class="d-flex align-items-center mb-2">
                            <div class="flex-grow-1">
                                <div class="small font-weight-bold">@user.UserName</div>
                                <div class="progress" style="height: 6px;">
                                    <div class="progress-bar bg-info" role="progressbar" 
                                         style="width: @(Math.Min(100, user.EventCount * 100 / Model.TopUsers.Max(u => u.EventCount)))%"></div>
                                </div>
                            </div>
                            <div class="ms-2">
                                <span class="badge bg-secondary">@user.EventCount</span>
                            </div>
                        </div>
                    }
                }
                else
                {
                    <p class="text-muted">暂无用户活动数据</p>
                }
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        // 事件趋势图
        const ctx = document.getElementById('eventTrendChart').getContext('2d');
        const eventTrendChart = new Chart(ctx, {
            type: 'line',
            data: {
                labels: @Html.Raw(Json.Serialize(Model.EventTrend.Select(e => e.Date.ToString("MM-dd")))),
                datasets: [{
                    label: '事件数量',
                    data: @Html.Raw(Json.Serialize(Model.EventTrend.Select(e => e.Count))),
                    borderColor: 'rgb(75, 192, 192)',
                    backgroundColor: 'rgba(75, 192, 192, 0.2)',
                    tension: 0.1
                }]
            },
            options: {
                responsive: true,
                plugins: {
                    legend: {
                        display: false
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true
                    }
                }
            }
        });
    </script>
}
