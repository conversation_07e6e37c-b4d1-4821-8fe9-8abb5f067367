@echo off
chcp 65001 >nul
echo ========================================
echo    文件监控系统启动器
echo ========================================
echo.

echo 正在启动文件监控系统...
echo.

echo [1/2] 启动 Web 管理界面...
cd FileMonitor.Web
start "FileMonitor Web UI" cmd /k "dotnet run --urls http://localhost:5001"
cd ..

echo [2/2] 等待服务启动...
timeout /t 5 /nobreak >nul

echo.
echo ✅ 系统启动完成！
echo.
echo 📊 Web 管理界面: http://localhost:5001
echo 🔧 功能模块:
echo    - 仪表板: 系统概览和实时统计
echo    - 访问日志: 查看和搜索文件访问记录
echo    - 告警管理: 查看和处理安全告警
echo    - 系统管理: 配置和监控系统状态
echo.
echo 💡 提示: 
echo    - 确保 MySQL 数据库服务正在运行
echo    - 首次使用请先运行数据库初始化脚本
echo    - 可以修改 Rules\rules.yaml 来自定义监控规则
echo.

echo 正在打开 Web 界面...
timeout /t 3 /nobreak >nul
start http://localhost:5001

echo.
echo 按任意键退出启动器...
pause >nul
