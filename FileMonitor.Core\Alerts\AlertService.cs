using System;
using System.Collections.Generic;
using System.Net;
using System.Net.Mail;
using System.Threading.Tasks;
using FileMonitor.Core.Configuration;
using FileMonitor.Core.Database;
using FileMonitor.Core.Models;
using FileMonitor.Core.Rules;
using Microsoft.Extensions.Logging;

namespace FileMonitor.Core.Alerts
{
    /// <summary>
    /// 告警服务类
    /// </summary>
    public class AlertService
    {
        private readonly DatabaseService _databaseService;
        private readonly EmailConfig _emailConfig;
        private readonly ILogger<AlertService> _logger;

        public AlertService(
            DatabaseService databaseService, 
            EmailConfig emailConfig, 
            ILogger<AlertService> logger)
        {
            _databaseService = databaseService ?? throw new ArgumentNullException(nameof(databaseService));
            _emailConfig = emailConfig ?? throw new ArgumentNullException(nameof(emailConfig));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }

        /// <summary>
        /// 处理告警事件
        /// </summary>
        /// <param name="alertArgs">告警事件参数</param>
        public async Task HandleAlertAsync(AlertTriggeredEventArgs alertArgs)
        {
            try
            {
                // 保存告警记录到数据库
                var alertHistory = new AlertHistory
                {
                    LogId = 0, // 这里需要从日志中获取ID
                    RuleName = alertArgs.Rule.Name,
                    AlertTime = alertArgs.AlertTime,
                    AlertMessage = alertArgs.Message,
                    IsProcessed = false
                };

                await _databaseService.SaveAlertAsync(alertHistory);

                // 根据配置的告警方式发送通知
                foreach (var method in alertArgs.Rule.Alert.Methods)
                {
                    switch (method.ToLower())
                    {
                        case "email":
                            await SendEmailAlertAsync(alertArgs);
                            break;
                        case "log":
                            LogAlert(alertArgs);
                            break;
                        case "webhook":
                            await SendWebhookAlertAsync(alertArgs);
                            break;
                        default:
                            _logger.LogWarning("未知的告警方式: {Method}", method);
                            break;
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "处理告警时发生错误: {RuleName}", alertArgs.Rule.Name);
            }
        }

        /// <summary>
        /// 发送邮件告警
        /// </summary>
        private async Task SendEmailAlertAsync(AlertTriggeredEventArgs alertArgs)
        {
            try
            {
                if (string.IsNullOrEmpty(_emailConfig.SmtpServer) || 
                    string.IsNullOrEmpty(_emailConfig.FromEmail))
                {
                    _logger.LogWarning("邮件配置不完整，跳过邮件告警");
                    return;
                }

                using var client = new SmtpClient(_emailConfig.SmtpServer, _emailConfig.SmtpPort)
                {
                    EnableSsl = _emailConfig.EnableSsl,
                    Credentials = new NetworkCredential(_emailConfig.FromEmail, _emailConfig.FromPassword)
                };

                var subject = $"[文件监控告警] {alertArgs.Rule.Name} - {alertArgs.Rule.Level}";
                var body = CreateEmailBody(alertArgs);

                var recipients = alertArgs.Rule.Alert.Recipients.Count > 0 
                    ? alertArgs.Rule.Alert.Recipients 
                    : _emailConfig.ToEmails;

                foreach (var recipient in recipients)
                {
                    var message = new MailMessage(_emailConfig.FromEmail, recipient, subject, body)
                    {
                        IsBodyHtml = true
                    };

                    await client.SendMailAsync(message);
                    _logger.LogInformation("邮件告警已发送至: {Recipient}", recipient);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "发送邮件告警失败");
            }
        }

        /// <summary>
        /// 创建邮件正文
        /// </summary>
        private string CreateEmailBody(AlertTriggeredEventArgs alertArgs)
        {
            var levelColor = alertArgs.Rule.Level switch
            {
                AlertLevel.Low => "#28a745",
                AlertLevel.Medium => "#ffc107",
                AlertLevel.High => "#fd7e14",
                AlertLevel.Critical => "#dc3545",
                _ => "#6c757d"
            };

            return $@"
<!DOCTYPE html>
<html>
<head>
    <meta charset='utf-8'>
    <title>文件监控告警</title>
</head>
<body style='font-family: Arial, sans-serif; margin: 20px;'>
    <div style='border-left: 4px solid {levelColor}; padding-left: 20px;'>
        <h2 style='color: {levelColor}; margin-top: 0;'>
            🚨 文件监控告警 - {alertArgs.Rule.Level}
        </h2>
        
        <div style='background-color: #f8f9fa; padding: 15px; border-radius: 5px; margin: 15px 0;'>
            <h3>告警信息</h3>
            <p><strong>规则名称:</strong> {alertArgs.Rule.Name}</p>
            <p><strong>告警时间:</strong> {alertArgs.AlertTime:yyyy-MM-dd HH:mm:ss}</p>
            <p><strong>告警级别:</strong> <span style='color: {levelColor}; font-weight: bold;'>{alertArgs.Rule.Level}</span></p>
            <p><strong>告警消息:</strong> {alertArgs.Message}</p>
        </div>

        <div style='background-color: #e9ecef; padding: 15px; border-radius: 5px; margin: 15px 0;'>
            <h3>文件访问详情</h3>
            <p><strong>文件路径:</strong> {alertArgs.Log.FilePath}</p>
            <p><strong>操作类型:</strong> {alertArgs.Log.Action}</p>
            <p><strong>用户名:</strong> {alertArgs.Log.UserName}</p>
            <p><strong>源IP地址:</strong> {alertArgs.Log.SourceIP}</p>
            <p><strong>事件时间:</strong> {alertArgs.Log.EventTime:yyyy-MM-dd HH:mm:ss}</p>
        </div>

        <div style='background-color: #d1ecf1; padding: 15px; border-radius: 5px; margin: 15px 0;'>
            <h3>规则描述</h3>
            <p>{alertArgs.Rule.Description}</p>
        </div>

        <hr style='margin: 20px 0;'>
        <p style='color: #6c757d; font-size: 12px;'>
            此邮件由文件监控系统自动发送，请勿直接回复。<br>
            如有疑问，请联系系统管理员。
        </p>
    </div>
</body>
</html>";
        }

        /// <summary>
        /// 记录告警日志
        /// </summary>
        private void LogAlert(AlertTriggeredEventArgs alertArgs)
        {
            var logLevel = alertArgs.Rule.Level switch
            {
                AlertLevel.Low => LogLevel.Information,
                AlertLevel.Medium => LogLevel.Warning,
                AlertLevel.High => LogLevel.Error,
                AlertLevel.Critical => LogLevel.Critical,
                _ => LogLevel.Warning
            };

            _logger.Log(logLevel, 
                "告警触发 - 规则: {RuleName}, 级别: {Level}, 文件: {FilePath}, 用户: {UserName}, 操作: {Action}",
                alertArgs.Rule.Name,
                alertArgs.Rule.Level,
                alertArgs.Log.FilePath,
                alertArgs.Log.UserName,
                alertArgs.Log.Action);
        }

        /// <summary>
        /// 发送Webhook告警
        /// </summary>
        private async Task SendWebhookAlertAsync(AlertTriggeredEventArgs alertArgs)
        {
            try
            {
                // 这里可以实现Webhook通知，比如发送到企业微信、钉钉等
                // 示例实现发送到通用Webhook端点
                
                var payload = new
                {
                    alert_type = "file_monitor",
                    rule_name = alertArgs.Rule.Name,
                    level = alertArgs.Rule.Level.ToString(),
                    message = alertArgs.Message,
                    timestamp = alertArgs.AlertTime,
                    file_path = alertArgs.Log.FilePath,
                    user_name = alertArgs.Log.UserName,
                    action = alertArgs.Log.Action,
                    source_ip = alertArgs.Log.SourceIP
                };

                // 这里需要根据实际的Webhook端点进行实现
                _logger.LogInformation("Webhook告警准备发送: {RuleName}", alertArgs.Rule.Name);
                
                // TODO: 实现实际的HTTP POST请求到Webhook端点
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "发送Webhook告警失败");
            }
        }

        /// <summary>
        /// 测试邮件配置
        /// </summary>
        public async Task<bool> TestEmailConfigAsync()
        {
            try
            {
                if (string.IsNullOrEmpty(_emailConfig.SmtpServer) || 
                    string.IsNullOrEmpty(_emailConfig.FromEmail))
                {
                    return false;
                }

                using var client = new SmtpClient(_emailConfig.SmtpServer, _emailConfig.SmtpPort)
                {
                    EnableSsl = _emailConfig.EnableSsl,
                    Credentials = new NetworkCredential(_emailConfig.FromEmail, _emailConfig.FromPassword)
                };

                var testMessage = new MailMessage(
                    _emailConfig.FromEmail, 
                    _emailConfig.FromEmail, 
                    "文件监控系统 - 邮件配置测试", 
                    "这是一封测试邮件，用于验证邮件配置是否正确。");

                await client.SendMailAsync(testMessage);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "邮件配置测试失败");
                return false;
            }
        }
    }
}
