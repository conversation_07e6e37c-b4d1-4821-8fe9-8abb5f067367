@model FileMonitor.Web.Models.LogListViewModel
@{
    ViewData["Title"] = "访问日志";
}

@section PageActions {
    <div class="btn-group" role="group">
        <a href="/Logs/Export?@(Context.Request.QueryString)" class="btn btn-outline-secondary btn-sm">
            <i class="bi bi-download"></i> 导出
        </a>
        <button type="button" class="btn btn-outline-primary btn-sm" data-bs-toggle="modal" data-bs-target="#searchModal">
            <i class="bi bi-search"></i> 搜索
        </button>
    </div>
}

<!-- 筛选器 -->
<div class="card mb-4">
    <div class="card-body">
        <form method="get" class="row g-3">
            <div class="col-md-3">
                <label for="filePath" class="form-label">文件路径</label>
                <input type="text" class="form-control" id="filePath" name="filePath" value="@Model.Filter.FilePath" placeholder="输入文件路径...">
            </div>
            <div class="col-md-2">
                <label for="userName" class="form-label">用户名</label>
                <input type="text" class="form-control" id="userName" name="userName" value="@Model.Filter.UserName" placeholder="用户名...">
            </div>
            <div class="col-md-2">
                <label for="action" class="form-label">操作类型</label>
                <select class="form-select" id="action" name="action">
                    <option value="">全部</option>
                    <option value="Read" @(Model.Filter.Action == "Read" ? "selected" : "")>读取</option>
                    <option value="Write" @(Model.Filter.Action == "Write" ? "selected" : "")>写入</option>
                    <option value="Delete" @(Model.Filter.Action == "Delete" ? "selected" : "")>删除</option>
                    <option value="Create" @(Model.Filter.Action == "Create" ? "selected" : "")>创建</option>
                    <option value="Rename" @(Model.Filter.Action == "Rename" ? "selected" : "")>重命名</option>
                </select>
            </div>
            <div class="col-md-2">
                <label for="startDate" class="form-label">开始日期</label>
                <input type="date" class="form-control" id="startDate" name="startDate" value="@Model.Filter.StartDate?.ToString("yyyy-MM-dd")">
            </div>
            <div class="col-md-2">
                <label for="endDate" class="form-label">结束日期</label>
                <input type="date" class="form-control" id="endDate" name="endDate" value="@Model.Filter.EndDate?.ToString("yyyy-MM-dd")">
            </div>
            <div class="col-md-1">
                <label class="form-label">&nbsp;</label>
                <div>
                    <button type="submit" class="btn btn-primary">
                        <i class="bi bi-funnel"></i> 筛选
                    </button>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- 日志列表 -->
<div class="card">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h6 class="m-0">访问日志 (共 @Model.TotalCount 条记录)</h6>
        <div class="d-flex align-items-center">
            <span class="me-2">每页显示:</span>
            <select class="form-select form-select-sm" style="width: auto;" onchange="changePageSize(this.value)">
                <option value="20" @(Model.Filter.PageSize == 20 ? "selected" : "")>20</option>
                <option value="50" @(Model.Filter.PageSize == 50 ? "selected" : "")>50</option>
                <option value="100" @(Model.Filter.PageSize == 100 ? "selected" : "")>100</option>
            </select>
        </div>
    </div>
    <div class="card-body p-0">
        @if (Model.Logs.Any())
        {
            <div class="table-responsive">
                <table class="table table-hover mb-0">
                    <thead class="table-light">
                        <tr>
                            <th>时间</th>
                            <th>文件路径</th>
                            <th>操作</th>
                            <th>用户</th>
                            <th>来源IP</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach (var log in Model.Logs)
                        {
                            <tr>
                                <td>
                                    <small>@log.EventTime.ToString("yyyy-MM-dd")</small><br>
                                    <small class="text-muted">@log.EventTime.ToString("HH:mm:ss")</small>
                                </td>
                                <td>
                                    <div class="text-truncate" style="max-width: 300px;" title="@log.FilePath">
                                        @log.FilePath
                                    </div>
                                </td>
                                <td>
                                    <span class="badge bg-@GetActionBadgeClass(log.Action)">@log.Action</span>
                                </td>
                                <td>@log.UserName</td>
                                <td>@log.SourceIP</td>
                                <td>
                                    <a href="/Logs/Details/@log.Id" class="btn btn-sm btn-outline-primary">
                                        <i class="bi bi-eye"></i> 详情
                                    </a>
                                </td>
                            </tr>
                        }
                    </tbody>
                </table>
            </div>
        }
        else
        {
            <div class="text-center py-5">
                <i class="bi bi-inbox display-1 text-muted"></i>
                <p class="text-muted mt-3">没有找到匹配的日志记录</p>
            </div>
        }
    </div>
</div>

<!-- 分页 -->
@if (Model.TotalPages > 1)
{
    <nav aria-label="日志分页" class="mt-4">
        <ul class="pagination justify-content-center">
            <li class="page-item @(Model.Filter.Page <= 1 ? "disabled" : "")">
                <a class="page-link" href="@GetPageUrl(Model.Filter.Page - 1)">上一页</a>
            </li>
            
            @for (int i = Math.Max(1, Model.Filter.Page - 2); i <= Math.Min(Model.TotalPages, Model.Filter.Page + 2); i++)
            {
                <li class="page-item @(i == Model.Filter.Page ? "active" : "")">
                    <a class="page-link" href="@GetPageUrl(i)">@i</a>
                </li>
            }
            
            <li class="page-item @(Model.Filter.Page >= Model.TotalPages ? "disabled" : "")">
                <a class="page-link" href="@GetPageUrl(Model.Filter.Page + 1)">下一页</a>
            </li>
        </ul>
    </nav>
}

<!-- 搜索模态框 -->
<div class="modal fade" id="searchModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">快速搜索</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="mb-3">
                    <label for="searchTerm" class="form-label">搜索关键词</label>
                    <input type="text" class="form-control" id="searchTerm" placeholder="输入文件路径、用户名或操作类型...">
                </div>
                <div id="searchResults" class="mt-3"></div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                <button type="button" class="btn btn-primary" onclick="performSearch()">搜索</button>
            </div>
        </div>
    </div>
</div>

@functions {
    private string GetActionBadgeClass(string action)
    {
        return action?.ToLower() switch
        {
            "read" => "primary",
            "write" => "warning",
            "delete" => "danger",
            "create" => "success",
            "rename" => "info",
            _ => "secondary"
        };
    }

    private string GetPageUrl(int page)
    {
        var query = Context.Request.Query.ToDictionary(x => x.Key, x => x.Value.ToString());
        query["page"] = page.ToString();
        return "/Logs?" + string.Join("&", query.Select(x => $"{x.Key}={x.Value}"));
    }
}

@section Scripts {
    <script>
        function changePageSize(pageSize) {
            const url = new URL(window.location);
            url.searchParams.set('pageSize', pageSize);
            url.searchParams.set('page', '1');
            window.location = url.toString();
        }

        function performSearch() {
            const searchTerm = document.getElementById('searchTerm').value;
            if (!searchTerm.trim()) return;

            fetch('/Logs/Search', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    searchTerm: searchTerm,
                    limit: 10
                })
            })
            .then(response => response.json())
            .then(data => {
                const resultsDiv = document.getElementById('searchResults');
                if (data.length > 0) {
                    let html = '<div class="list-group">';
                    data.forEach(log => {
                        html += `
                            <a href="/Logs/Details/${log.id}" class="list-group-item list-group-item-action">
                                <div class="d-flex w-100 justify-content-between">
                                    <h6 class="mb-1">${log.filePath}</h6>
                                    <small>${new Date(log.eventTime).toLocaleString()}</small>
                                </div>
                                <p class="mb-1">用户: ${log.userName} | 操作: ${log.action}</p>
                            </a>
                        `;
                    });
                    html += '</div>';
                    resultsDiv.innerHTML = html;
                } else {
                    resultsDiv.innerHTML = '<p class="text-muted">没有找到匹配的结果</p>';
                }
            })
            .catch(error => {
                console.error('搜索失败:', error);
                document.getElementById('searchResults').innerHTML = '<p class="text-danger">搜索失败，请重试</p>';
            });
        }

        // 回车键搜索
        document.getElementById('searchTerm').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                performSearch();
            }
        });
    </script>
}
