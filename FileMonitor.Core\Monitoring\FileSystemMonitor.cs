using System;
using System.Collections.Generic;
using System.IO;
using System.Security.Principal;
using System.Threading.Tasks;
using FileMonitor.Core.Models;
using FileMonitor.Core.Database;
using Microsoft.Extensions.Logging;

namespace FileMonitor.Core.Monitoring
{
    /// <summary>
    /// 文件系统监控器
    /// </summary>
    public class FileSystemMonitor : IDisposable
    {
        private readonly List<FileSystemWatcher> _watchers;
        private readonly DatabaseService _databaseService;
        private readonly ILogger<FileSystemMonitor> _logger;
        private readonly List<FileAccessLog> _logBuffer;
        private readonly object _bufferLock = new object();
        private bool _disposed = false;

        public event EventHandler<FileAccessLog>? FileAccessDetected;

        public FileSystemMonitor(DatabaseService databaseService, ILogger<FileSystemMonitor> logger)
        {
            _watchers = new List<FileSystemWatcher>();
            _databaseService = databaseService ?? throw new ArgumentNullException(nameof(databaseService));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _logBuffer = new List<FileAccessLog>();
        }

        /// <summary>
        /// 开始监控指定路径
        /// </summary>
        /// <param name="paths">要监控的路径列表</param>
        public void StartMonitoring(IEnumerable<string> paths)
        {
            foreach (var path in paths)
            {
                try
                {
                    if (!Directory.Exists(path))
                    {
                        _logger.LogWarning("监控路径不存在: {Path}", path);
                        continue;
                    }

                    var watcher = new FileSystemWatcher(path)
                    {
                        NotifyFilter = NotifyFilters.FileName | 
                                     NotifyFilters.DirectoryName | 
                                     NotifyFilters.LastWrite | 
                                     NotifyFilters.Security,
                        IncludeSubdirectories = true,
                        EnableRaisingEvents = true
                    };

                    // 订阅事件
                    watcher.Created += OnFileEvent;
                    watcher.Deleted += OnFileEvent;
                    watcher.Changed += OnFileEvent;
                    watcher.Renamed += OnRenamed;
                    watcher.Error += OnError;

                    _watchers.Add(watcher);
                    _logger.LogInformation("开始监控路径: {Path}", path);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "启动路径监控失败: {Path}", path);
                }
            }
        }

        /// <summary>
        /// 停止所有监控
        /// </summary>
        public void StopMonitoring()
        {
            foreach (var watcher in _watchers)
            {
                try
                {
                    watcher.EnableRaisingEvents = false;
                    watcher.Dispose();
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "停止监控时发生错误");
                }
            }
            _watchers.Clear();
            _logger.LogInformation("已停止所有文件系统监控");
        }

        /// <summary>
        /// 处理文件事件
        /// </summary>
        private void OnFileEvent(object sender, FileSystemEventArgs e)
        {
            try
            {
                var log = CreateFileAccessLog(e.FullPath, e.ChangeType.ToString());
                ProcessFileAccessLog(log);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "处理文件事件时发生错误: {Path}", e.FullPath);
            }
        }

        /// <summary>
        /// 处理文件重命名事件
        /// </summary>
        private void OnRenamed(object sender, RenamedEventArgs e)
        {
            try
            {
                var log = CreateFileAccessLog(e.FullPath, "Renamed");
                log.Details = $"从 {e.OldFullPath} 重命名为 {e.FullPath}";
                ProcessFileAccessLog(log);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "处理文件重命名事件时发生错误: {Path}", e.FullPath);
            }
        }

        /// <summary>
        /// 处理监控错误
        /// </summary>
        private void OnError(object sender, ErrorEventArgs e)
        {
            _logger.LogError(e.GetException(), "文件系统监控发生错误");
        }

        /// <summary>
        /// 创建文件访问日志对象
        /// </summary>
        private FileAccessLog CreateFileAccessLog(string filePath, string action)
        {
            var currentUser = WindowsIdentity.GetCurrent();
            
            return new FileAccessLog
            {
                EventTime = DateTime.Now,
                FilePath = filePath,
                Action = action,
                UserName = currentUser?.Name ?? "Unknown",
                SourceIP = GetLocalIPAddress(),
                Details = $"通过FileSystemWatcher检测到的{action}操作"
            };
        }

        /// <summary>
        /// 处理文件访问日志
        /// </summary>
        private void ProcessFileAccessLog(FileAccessLog log)
        {
            // 触发事件
            FileAccessDetected?.Invoke(this, log);

            // 添加到缓冲区
            lock (_bufferLock)
            {
                _logBuffer.Add(log);
            }

            // 异步保存到数据库
            Task.Run(async () =>
            {
                try
                {
                    await _databaseService.SaveLogAsync(log);
                    _logger.LogDebug("文件访问日志已保存: {FilePath} - {Action}", log.FilePath, log.Action);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "保存文件访问日志失败: {FilePath}", log.FilePath);
                }
            });
        }

        /// <summary>
        /// 获取本地IP地址
        /// </summary>
        private string GetLocalIPAddress()
        {
            try
            {
                var hostName = System.Net.Dns.GetHostName();
                var addresses = System.Net.Dns.GetHostAddresses(hostName);
                
                foreach (var address in addresses)
                {
                    if (address.AddressFamily == System.Net.Sockets.AddressFamily.InterNetwork)
                    {
                        return address.ToString();
                    }
                }
                return "127.0.0.1";
            }
            catch
            {
                return "127.0.0.1";
            }
        }

        /// <summary>
        /// 批量刷新缓冲区到数据库
        /// </summary>
        public async Task FlushBufferAsync()
        {
            List<FileAccessLog> logsToSave;
            
            lock (_bufferLock)
            {
                if (_logBuffer.Count == 0)
                    return;
                
                logsToSave = new List<FileAccessLog>(_logBuffer);
                _logBuffer.Clear();
            }

            try
            {
                await _databaseService.SaveLogsAsync(logsToSave);
                _logger.LogInformation("批量保存了 {Count} 条文件访问日志", logsToSave.Count);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "批量保存文件访问日志失败");
                
                // 如果保存失败，将日志重新加入缓冲区
                lock (_bufferLock)
                {
                    _logBuffer.InsertRange(0, logsToSave);
                }
            }
        }

        /// <summary>
        /// 获取缓冲区中的日志数量
        /// </summary>
        public int GetBufferCount()
        {
            lock (_bufferLock)
            {
                return _logBuffer.Count;
            }
        }

        public void Dispose()
        {
            if (!_disposed)
            {
                StopMonitoring();
                _disposed = true;
            }
        }
    }
}
