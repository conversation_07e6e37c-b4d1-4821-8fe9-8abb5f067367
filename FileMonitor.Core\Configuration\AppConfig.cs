using System;
using System.Collections.Generic;

namespace FileMonitor.Core.Configuration
{
    /// <summary>
    /// 应用程序配置类
    /// </summary>
    public class AppConfig
    {
        /// <summary>
        /// 数据库连接字符串
        /// </summary>
        public string ConnectionString { get; set; } = string.Empty;

        /// <summary>
        /// 监控路径列表
        /// </summary>
        public List<string> MonitorPaths { get; set; } = new List<string>();

        /// <summary>
        /// 是否启用Windows事件日志监控
        /// </summary>
        public bool EnableEventLogMonitoring { get; set; } = true;

        /// <summary>
        /// 是否启用文件系统监控
        /// </summary>
        public bool EnableFileSystemMonitoring { get; set; } = true;

        /// <summary>
        /// 日志级别
        /// </summary>
        public string LogLevel { get; set; } = "Information";

        /// <summary>
        /// 日志文件路径
        /// </summary>
        public string LogFilePath { get; set; } = "logs/filemonitor.log";

        /// <summary>
        /// 规则配置文件路径
        /// </summary>
        public string RulesConfigPath { get; set; } = "Rules/rules.yaml";

        /// <summary>
        /// 邮件通知配置
        /// </summary>
        public EmailConfig Email { get; set; } = new EmailConfig();

        /// <summary>
        /// 性能配置
        /// </summary>
        public PerformanceConfig Performance { get; set; } = new PerformanceConfig();
    }

    /// <summary>
    /// 邮件配置类
    /// </summary>
    public class EmailConfig
    {
        /// <summary>
        /// SMTP服务器地址
        /// </summary>
        public string SmtpServer { get; set; } = string.Empty;

        /// <summary>
        /// SMTP端口
        /// </summary>
        public int SmtpPort { get; set; } = 587;

        /// <summary>
        /// 发送者邮箱
        /// </summary>
        public string FromEmail { get; set; } = string.Empty;

        /// <summary>
        /// 发送者密码
        /// </summary>
        public string FromPassword { get; set; } = string.Empty;

        /// <summary>
        /// 接收者邮箱列表
        /// </summary>
        public List<string> ToEmails { get; set; } = new List<string>();

        /// <summary>
        /// 是否启用SSL
        /// </summary>
        public bool EnableSsl { get; set; } = true;
    }

    /// <summary>
    /// 性能配置类
    /// </summary>
    public class PerformanceConfig
    {
        /// <summary>
        /// 批量写入数据库的大小
        /// </summary>
        public int BatchSize { get; set; } = 100;

        /// <summary>
        /// 批量写入的时间间隔（秒）
        /// </summary>
        public int BatchIntervalSeconds { get; set; } = 30;

        /// <summary>
        /// 内存缓存的最大条目数
        /// </summary>
        public int MaxCacheSize { get; set; } = 1000;

        /// <summary>
        /// 日志保留天数
        /// </summary>
        public int LogRetentionDays { get; set; } = 90;
    }
}
