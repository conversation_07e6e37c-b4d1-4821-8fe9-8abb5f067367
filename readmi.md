
# 文件监控系统 (FileMonitorSystem)

一套用于监控内网 Windows Server 文件服务器内容访问的完整解决方案。

## 功能特性

- **实时文件监控**: 使用 FileSystemWatcher 监控文件系统变化
- **Windows 事件日志分析**: 解析安全日志中的文件访问事件 (Event ID 4663, 4656, 4658)
- **智能规则引擎**: 基于 YAML 配置的灵活规则系统
- **多级告警系统**: 支持邮件、日志、Webhook 等多种告警方式
- **数据持久化**: 使用 MySQL 数据库存储访问日志和告警记录
- **Windows 服务**: 作为 Windows 服务运行，支持自动启动和恢复
- **性能优化**: 批量处理、缓存机制、异步操作

## 系统架构

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   文件系统监控   │    │   事件日志监控    │    │   规则引擎      │
│ FileSystemWatcher│    │  EventLogMonitor │    │  RuleEngine     │
└─────────┬───────┘    └─────────┬────────┘    └─────────┬───────┘
          │                      │                       │
          └──────────────────────┼───────────────────────┘
                                 │
                    ┌────────────▼────────────┐
                    │     文件访问日志         │
                    │   FileAccessLog        │
                    └────────────┬────────────┘
                                 │
                    ┌────────────▼────────────┐
                    │      告警服务           │
                    │    AlertService        │
                    └────────────┬────────────┘
                                 │
          ┌──────────────────────┼──────────────────────┐
          │                      │                      │
    ┌─────▼─────┐         ┌──────▼──────┐        ┌──────▼──────┐
    │   邮件    │         │    日志     │        │   数据库    │
    │  Email    │         │    Log      │        │   MySQL     │
    └───────────┘         └─────────────┘        └─────────────┘
```

## 快速开始

### 环境要求

- Windows Server 2019/2022 或 Windows 10/11
- .NET 8.0 Runtime
- MySQL 8.0+
- 管理员权限（用于安装 Windows 服务和访问安全日志）

### 1. 数据库准备

1. 安装 MySQL 8.0+
2. 创建数据库和用户：

```sql
-- 创建数据库
CREATE DATABASE FileMonitorDB CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 创建用户（生产环境建议使用专用账户）
CREATE USER 'filemonitor'@'localhost' IDENTIFIED BY 'your_secure_password';
GRANT ALL PRIVILEGES ON FileMonitorDB.* TO 'filemonitor'@'localhost';
FLUSH PRIVILEGES;
```

3. 运行数据库初始化脚本：

```bash
mysql -u root -p FileMonitorDB < Database/init_database.sql
```

### 2. 编译和发布

```bash
# 克隆项目
git clone <repository-url>
cd FileMonitorSystem

# 构建和发布
.\Scripts\build-and-deploy.ps1 -Configuration Release
```

### 3. 配置系统

1. 修改配置文件 `publish/appsettings.json`：

```json
{
  "ConnectionString": "Server=127.0.0.1;Database=FileMonitorDB;User=root;Password=******;Charset=utf8mb4;",
  "MonitorPaths": [
    "C:\\SharedFiles",
    "C:\\ImportantDocuments"
  ],
  "Email": {
    "SmtpServer": "your-smtp-server.com",
    "FromEmail": "<EMAIL>",
    "ToEmails": ["<EMAIL>"]
  }
}
```

2. 配置监控规则 `publish/Rules/rules.yaml`（可选，已包含默认规则）

### 4. 安装和启动服务

```powershell
# 以管理员身份运行 PowerShell
.\Scripts\install-service.ps1

# 或者使用构建脚本直接安装
.\Scripts\build-and-deploy.ps1 -InstallService -StartService
```

### 5. 验证安装

```powershell
# 检查服务状态
Get-Service -Name FileMonitorService

# 查看服务日志
Get-EventLog -LogName Application -Source FileMonitorService -Newest 10

# 运行测试脚本
.\Scripts\test-system.ps1
```

## 配置说明

### 主配置文件 (appsettings.json)

| 配置项 | 说明 | 默认值 |
|--------|------|--------|
| ConnectionString | MySQL 连接字符串 | - |
| MonitorPaths | 监控路径列表 | [] |
| EnableEventLogMonitoring | 启用事件日志监控 | true |
| EnableFileSystemMonitoring | 启用文件系统监控 | true |
| Email.SmtpServer | SMTP 服务器地址 | - |
| Performance.BatchSize | 批量处理大小 | 100 |

### 规则配置文件 (Rules/rules.yaml)

支持以下规则类型：

- **文件路径匹配**: 使用正则表达式匹配文件路径
- **用户过滤**: 基于用户名的访问控制
- **操作类型**: 监控特定的文件操作（读取、写入、删除等）
- **时间范围**: 非工作时间访问检测
- **文件类型**: 基于文件扩展名的过滤
- **文件大小**: 大文件操作检测

示例规则：

```yaml
rules:
  - name: "敏感文件访问告警"
    description: "检测对包含敏感关键词的文件访问"
    enabled: true
    level: High
    conditions:
      pathPattern: ".*[机密|保密|confidential].*\\.(xlsx?|docx?|pdf)$"
      actions: ["Read", "Write", "Delete"]
    alert:
      methods: ["email", "log"]
      recipients: ["<EMAIL>"]
      throttleSeconds: 300
```

## 监控能力

### 文件操作检测

- ✅ 文件创建 (Create)
- ✅ 文件读取 (Read)
- ✅ 文件写入 (Write)
- ✅ 文件删除 (Delete)
- ✅ 文件重命名 (Rename)
- ✅ 权限变更 (Security)

### 告警级别

- 🟢 **Low**: 一般信息，如周末访问
- 🟡 **Medium**: 需要关注，如非工作时间访问
- 🟠 **High**: 重要告警，如敏感文件访问
- 🔴 **Critical**: 严重告警，如系统文件修改

### 告警方式

- 📧 **邮件通知**: 支持 HTML 格式的详细告警邮件
- 📝 **日志记录**: 写入 Windows 事件日志和应用日志
- 🔗 **Webhook**: 支持企业微信、钉钉等第三方集成

## 性能特性

- **异步处理**: 所有 I/O 操作均为异步，不阻塞监控
- **批量写入**: 定期批量写入数据库，减少数据库压力
- **内存缓存**: 智能缓存机制，避免重复处理
- **告警限流**: 防止告警风暴，支持按规则限流
- **自动清理**: 定期清理过期数据和缓存

## 安全考虑

- **最小权限**: 服务以最小必要权限运行
- **数据加密**: 支持数据库连接加密
- **审计日志**: 完整的操作审计记录
- **配置保护**: 敏感配置支持环境变量和加密存储

## 故障排除

### 常见问题

1. **服务无法启动**
   - 检查数据库连接
   - 验证配置文件格式
   - 确认服务权限

2. **监控无数据**
   - 检查监控路径是否存在
   - 验证文件系统权限
   - 确认规则配置正确

3. **告警不发送**
   - 检查邮件配置
   - 验证 SMTP 连接
   - 查看错误日志

### 日志查看

```powershell
# Windows 事件日志
Get-EventLog -LogName Application -Source FileMonitorService

# 应用程序日志文件
Get-Content "logs\filemonitor.log" -Tail 50

# 数据库日志查询
SELECT * FROM access_log ORDER BY event_time DESC LIMIT 100;
```

## 开发和扩展

### 项目结构

```
FileMonitorSystem/
├── FileMonitor.Core/          # 核心业务逻辑
│   ├── Models/               # 数据模型
│   ├── Database/             # 数据访问层
│   ├── Monitoring/           # 监控组件
│   ├── Rules/                # 规则引擎
│   ├── Alerts/               # 告警服务
│   └── Configuration/        # 配置管理
├── FileMonitor.Service/       # Windows 服务
├── Config/                   # 配置文件
├── Database/                 # 数据库脚本
├── Rules/                    # 规则配置
└── Scripts/                  # 部署脚本
```

### 自定义规则

可以通过修改 `Rules/rules.yaml` 文件添加自定义规则，或者扩展 `RuleEngine` 类实现更复杂的规则逻辑。

### 自定义告警

实现 `IAlertHandler` 接口可以添加新的告警方式，如短信、企业微信等。

## 许可证

本项目采用 MIT 许可证。详见 [LICENSE](LICENSE) 文件。

## 贡献

欢迎提交 Issue 和 Pull Request！

## 更新日志

### v1.0.0 (2025-07-18)
- 初始版本发布
- 支持文件系统和事件日志监控
- 基于 YAML 的规则引擎
- 邮件告警功能
- Windows 服务支持

1. 环境准备
(1) 安装MySQL驱动
<BASH>
# 在.NET项目中添加MySQL依赖
dotnet add package MySql.Data.EntityFramework
# 或使用ORM框架（推荐Dapper + MySQL连接库）
dotnet add package Dapper
dotnet add package MySql.Data
(2) 创建MySQL数据库
<SQL>
CREATE DATABASE FileMonitorDB;
CREATE TABLE access_log (
    id INT PRIMARY KEY AUTO_INCREMENT,
    event_time DATETIME NOT NULL,
    file_path TEXT NOT NULL,
    action VARCHAR(50),
    user_name VARCHAR(255),
    source_ip VARCHAR(45),
    details TEXT
);
2. 核心代码调整
(1) 配置连接字符串
在 appsettings.json 中添加配置（⚠️敏感信息建议用环境变量）：

<JSON>
{
  "ConnectionStrings": {
    "MySQL": "Server=127.0.0.1;Database=FileMonitorDB;User=root;Password=******;"
  }
}
(2) 数据访问层改造（Dapper示例）
<CSHARP>
using MySql.Data.MySqlClient;
using Dapper;
public class DatabaseService
{
    private readonly string _connectionString;
    public DatabaseService(IConfiguration config)
    {
        _connectionString = config.GetConnectionString("MySQL");
    }
    public void SaveLog(FileAccessLog log)
    {
        using var conn = new MySqlConnection(_connectionString);
        conn.Execute(
            @"INSERT INTO access_log 
            (event_time, file_path, action, user_name, source_ip, details) 
            VALUES (@Time, @FilePath, @Action, @User, @SourceIP, @Details)",
            new {
                log.Time,
                log.FilePath,
                log.Action,
                log.User,
                log.SourceIP,
                log.Details
            });
    }
}
(3) 实体类定义
<CSHARP>
public class FileAccessLog
{
    public DateTime Time { get; set; }
    public string FilePath { get; set; }
    public string Action { get; set; }
    public string User { get; set; }
    public string SourceIP { get; set; }
    public string Details { get; set; }
}
3. EF Core迁移配置（可选）
(1) DbContext配置
<CSHARP>
using Microsoft.EntityFrameworkCore;
public class AppDbContext : DbContext
{
    public DbSet<FileAccessLog> AccessLogs { get; set; }
    protected override void OnConfiguring(DbContextOptionsBuilder options)
    {
        options.UseMySql("Your_Connection_String", 
            ServerVersion.AutoDetect("Your_Connection_String"));
    }
}
(2) 执行迁移命令
<BASH>
dotnet ef migrations add InitialCreate
dotnet ef database update
4. 安全加固建议
<YAML>
# 生产环境必做：
1. 创建专用账户：
   CREATE USER 'monitor_user'@'localhost' IDENTIFIED BY 'StrongPassword123!';
   GRANT INSERT, SELECT ON FileMonitorDB.access_log TO 'monitor_user'@'localhost';
2. 加密连接：
   - MySQL启用SSL
   - 连接字符串中添加 "SslMode=Required"
3. 密码存储：
   - 从环境变量读取密码而非配置文件：
     "Password=Environment.GetEnvironmentVariable("DB_PASSWORD")"
5. 性能优化
(1) 数据库索引优化
<SQL>
ALTER TABLE access_log ADD INDEX idx_event_time (event_time);
ALTER TABLE access_log ADD INDEX idx_user_action (user_name, action);
(2) 批量插入优化
<CSHARP>
// 使用事务批量写入
using var transaction = conn.BeginTransaction();
foreach (var log in logs)
{
    conn.Execute(/*...*/, transaction: transaction);
}
transaction.Commit();
6. 部署验证
(1) 测试连接
<CSHARP>
try 
{
    using var conn = new MySqlConnection(_connectionString);
    conn.Open();
    Console.WriteLine("MySQL连接成功！");
}
catch (Exception ex)
{
    Console.WriteLine($"连接失败：{ex.Message}");
}
(2) 日志查询验证
<SQL>
SELECT * FROM access_log WHERE file_path LIKE '%机密%' ORDER BY event_time DESC LIMIT 10;
7. 方案对比（调整前VS调整后）
模块	原方案（MYSQL）	新方案（MySQL）
部署复杂度	无需额外服务	需独立部署MySQL
扩展性	仅适合单机	支持主从复制、分库分表
性能	轻量但并发弱	支持高并发，索引优化更灵活
常见问题处理
权限拒绝访问

<SQL>
-- 检查用户权限
SHOW GRANTS FOR 'root'@'localhost';
中文乱码

在连接字符串后追加：Charset=utf8mb4;

无法连接

确认MySQL服务是否运行：sudo systemctl status mysql
检查防火墙：sudo ufw allow 3306
通过以上调整，即可将文件监控系统的数据存储迁移到MySQL数据库。最终代码结构示例如下：

<BASH>
.
├── appsettings.json
├── Database/
│   └── DatabaseService.cs      # 数据访问类
├── Models/
│   └── FileAccessLog.cs        # 实体模型
└── FileMonitor.Service/
    └── Monitor.cs              # 主监控
Tokens:9506↑6131↓3375

用户
07/18 16:01
使用MYSQL 127.0.0.1 name:root password:******

Tokens: 21

deepseek/deepseek-r1:free | OpenRouter
07/18 15:52
已深度思考（用时 16.4 秒）

开发一套用于监控内网Windows Server文件服务器内容访问的程序需要结合文件系统监控、日志记录、告警机制和数据持久化功能。以下是分模块的设计方案和核心逻辑实现参考：

1. 架构设计
<TEXT>
                    +-------------------+
                    |  用户界面/API      |  <-> 配置管理、查看日志
                    +---------+---------+
                              |
               +--------------+-------------+
               | 监控服务（Windows服务/Daemon）| 
               +--------------+-------------+
                              |
               +--------------+--------------+
               | 文件系统监控模块            |  <-> 实时捕获文件操作
               | 事件日志解析模块            |  <-> 分析Windows安全事件
               | 规则引擎（过滤/告警）        |  <-> 匹配敏感操作规则
               | 数据存储模块（DB/文件）       |  <-> 存储日志、审计数据
               +-----------------------------+
2. 核心技术选型
模块	技术方案	依赖库/API
文件系统监控	FileSystemWatcher类 + Windows事件日志	System.IO, System.Diagnostics.Eventing
日志解析与增强	解析Windows安全日志（Event ID 4663及相关链式事件）	System.Diagnostics.Eventing.Reader
规则引擎	基于正则或yaml的规则配置	YamlDotNet, System.Text.RegularExpressions
数据存储	MYSQL（轻量）或SQL Server（企业级）	Entity Framework Core/Dapper
告警通知	SMTP邮件、Webhook、企业微信/钉钉机器人	System.Net.Mail, RestSharp
部署方式	打包为Windows服务（TopShelf库）	Topshelf
3. 核心代码实现
3.1 文件系统实时监控（C#示例）
<CSHARP>
public class FileMonitor 
{
    private FileSystemWatcher _watcher;
    
    public void Start(string path) 
    {
        _watcher = new FileSystemWatcher(path);
        _watcher.NotifyFilter = NotifyFilters.FileName | NotifyFilters.DirectoryName 
                               | NotifyFilters.Security | NotifyFilters.LastWrite;
        _watcher.Created += OnFileEvent;
        _watcher.Deleted += OnFileEvent;
        _watcher.Renamed += OnRenamed;
        _watcher.EnableRaisingEvents = true;
    }
    private void OnFileEvent(object sender, FileSystemEventArgs e) 
    {
        var log = new FileAccessLog {
            Time = DateTime.UtcNow,
            User = WindowsIdentity.GetCurrent().Name,
            FilePath = e.FullPath,
            Action = e.ChangeType.ToString()
        };
        SaveToDatabase(log);
        CheckAlertRules(log); // 触发规则引擎检测
    }
}
3.2 Windows安全日志解析（捕获Event ID 4663）
<CSHARP>
using (var reader = new EventLogReader("Security")) 
{
    EventLogRecord record;
    while ((record = reader.ReadEvent() as EventLogRecord) != null) 
    {
        if (record.Id == 4663) 
        {
            var filePath = record.Properties[6].Value.ToString();
            var username = record.Properties[1].Value.ToString();
            // 解析其他字段：IP地址、时间戳等
        }
    }
}
3.3 规则引擎（YAML规则示例）
<YAML>
rules:
  - name: "敏感文件读取告警"
    conditions:
      path: "/机密数据/.*\.xlsx$"
      action: "Read"
      user: "^(?!ADMIN).*$"  # 非管理员用户触发
    alert:
      method: "email"
      recipients: "<EMAIL>"
4. 数据库设计（MYSQL示例）
<SQL>
CREATE TABLE access_log (
    id INTEGER PRIMARY KEY,
    event_time DATETIME NOT NULL,
    file_path TEXT NOT NULL,
    action TEXT,  -- Read/Write/Delete
    user_name TEXT,
    source_ip TEXT,
    details TEXT  -- JSON扩展字段
);
CREATE TABLE alert_history (
    id INTEGER PRIMARY KEY,
    log_id INTEGER REFERENCES access_log(id),
    rule_name TEXT,
    alert_time DATETIME
);
5. 高级功能实现
5.1 分布式监控（多服务器支持）
使用 Agent模式 在每台文件服务器部署监控程序
数据汇总到中心服务器（通过HTTP API或消息队列，如RabbitMQ/Kafka）
5.2 用户行为分析
<CSHARP>
// 使用滑动窗口算法检测异常频繁访问
var threshold = 30; // 30次/分钟
var recentAccess = GetAccessCount(user, DateTime.Now.AddMinutes(-1));
if (recentAccess > threshold) {
    TriggerAlert("高频访问告警", user);
}
5.3 文件内容变更对比
<PYTHON>
# Python示例（集成到C#通过IronPython或独立服务）
def diff_content(old_path, new_path):
    import difflib
    with open(old_path, 'r') as f1, open(new_path, 'r') as f2:
        diff = difflib.unified_diff(f1.readlines(), f2.readlines())
    return ''.join(diff)
6. 安全增强措施
权限控制：监控程序以最小权限账户（如Network Service）运行
日志防篡改：将审计日志Hash写入区块链（如Azure Blockchain Service）
通信加密：Agent与中心服务器间使用HTTPS/mTLS
审计日志自动签名：使用RSA对关键日志签名
7. 部署与维护
打包方式：使用Inno Setup创建Windows安装包
升级机制：通过HTTP端点检查版本并自动更新
监控服务自恢复：利用Windows服务管理器自动重启崩溃进程
8. 性能优化策略
场景	优化手段
高并发文件操作	使用异步写入（async/await）+ 批量数据库提交
海量日志存储	按时间分片存储（如按月分表） + 数据冷热分离
实时告警延迟	使用内存计算（如Redis Streams暂存事件） + 滑动时间窗口统计
对比现成工具的优势
<TEXT>
| 维度           | 自研程序               | 商业工具（如Netwrix）         |
|----------------|------------------------|------------------------------|
| **定制化**      | 完全自主控制逻辑        | 功能受限于产品设计            | 
| **成本**        | 一次开发，无持续授权费  | 按用户/服务器收费，逐年累积    |
| **数据隐私**    | 数据完全内控           | 可能涉及第三方存储风险        |
| **集成能力**    | 可与企业内部系统深度对接 | 通常仅支持标准协议接口        |
代码仓库参考结构
<BASH>
.
├── src/
│   ├── FileMonitor.Service/      # 监控主服务
│   ├── FileMonitor.Web/          # 管理界面（可选Blazor/WPF）
│   └── FileMonitor.Agent/        # 分布式Agent程序
├── rules/                        # 规则配置文件
├── docs/                         # 部署文档
└── scripts/
    └── db_migration/             # 数据库升级脚本
该方案可根据需求裁剪为轻量级单机版（仅核心监控+MYSQL）或扩展为分布式企业级系统。建议采用CI/CD流水线（如GitHub Actions）实现自动化测试与部署。

Tokens:6110↑2292↓3818

用户
07/18 15:52
开发一套程序

Tokens: 6

deepseek/deepseek-r1:free | OpenRouter
07/18 15:51
已深度思考（用时 11.3 秒）

在Windows Server 2023中监控内网文件服务器的内容访问，需结合操作系统内置工具、审计策略及可能的第三方解决方案。以下是分步骤的详细方案：

1. 启用文件系统审计策略
步骤：
配置审计策略

打开 本地安全策略：运行 secpol.msc
路径：安全设置 > 本地策略 > 审计策略
启用 审核对象访问（在“成功”和“失败”事件上勾选）
设置文件/文件夹审计规则

右键目标文件夹 → 属性 → 安全 → 高级
进入 审计 选项卡 → 添加 → 选择用户/组（如 "Everyone" 或特定用户）
勾选需要记录的权限（如 "读取"、"写入"、"删除" 等）
查看日志

打开 事件查看器（eventvwr.msc）
路径：Windows 日志 > 安全日志
筛选事件ID：4663（文件操作）、4660（句柄关闭）等。
2. 使用第三方监控工具（推荐场景：企业级需求）
推荐工具：
Netwrix Auditor
提供实时文件访问监控、权限变更跟踪，支持生成合规报告。
ManageEngine File Audit Plus
可监控SMB/NFS文件操作，触发告警（如敏感文件被访问）。
SolarWinds Access Rights Manager
结合权限管理与访问监控，适合大规模环境。
优势：
可视化操作界面，避免手动筛选日志。
支持实时告警（如邮件/SMS通知）。
长期日志存储与分析功能。
3. 脚本化监控（适合轻量需求或自动化）
PowerShell自动提取文件访问日志
<POWERSHELL>
# 查询过去24小时内文件访问事件
Get-WinEvent -FilterHashtable @{
    LogName = 'Security'
    ID = 4663
    StartTime = (Get-Date).AddHours(-24)
} | Where-Object {
    $_.Properties[6].Value -like "*敏感文件夹*"
} | Select TimeCreated, Message
# 输出格式优化
Get-WinEvent ... | Format-Table TimeCreated, @{n='用户';e={$_.Properties[1].Value}}, @{n='文件路径';e={$_.Properties[6].Value}}
4. 高级配置与优化
a. 日志管理
增加安全日志大小：防止事件被覆盖（eventvwr.msc → 安全日志属性 → 最大日志大小）。
启用日志归档：将日志定期导出到专用存储，或配置日志服务器（如 Windows Event Forwarding）。
b. 实时监控集成
SIEM工具：将日志发送到 Splunk、Elasticsearch 或 Microsoft Sentinel，设置实时告警规则。
示例流程：
配置服务器为 WinRM 或 Syslog 输出。
在SIEM中创建规则，检测频繁访问敏感文件或异常时段操作。
c. 敏感文件特殊处理
使用 文件分类基础架构 (FCI)：
通过 文件服务器资源管理器 (FSRM) 对敏感文件打标签。
结合自定义脚本或工具触发特定审计动作。
5. 权限与安全最佳实践
最小权限原则：用户/组仅授予必要权限。
定期审查：
检查文件共享权限（icacls 命令或 Get-ACL）。
清理旧账户/无用权限。
防御性监控：对管理员账户（如 Administrator）的访问记录重点关注。
6. 常见问题排查
事件ID 4663不显示？
确认目标文件系统的格式为NTFS（审计仅支持NTFS）。
确保位巣组策略未覆盖本地策略（运行 gpresult /h report.html 检查策略继承）。
日志过多？
缩小审计范围（如仅监控关键文件）。
使用筛选条件或工具自动聚合数据。
总结
轻量级监控：内置审计策略 + PowerShell脚本。
企业级方案：第三方工具（如 Netwrix） + SIEM集成。
关键点：审计配置后需验证日志记录效果，定期优化策略以平衡安全与性能。
Tokens:2286↑105↓2181

用户
07/18 15:51
内网监控 windows server 2023 文件服务器 内容文件访问


