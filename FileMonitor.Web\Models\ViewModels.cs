using FileMonitor.Core.Models;
using System.ComponentModel.DataAnnotations;
using System.Diagnostics;

namespace FileMonitor.Web.Models
{
    // 仪表板视图模型
    public class DashboardViewModel
    {
        public int TodayEvents { get; set; }
        public int TodayAlerts { get; set; }
        public int UniqueUsers { get; set; }
        public int UniqueFiles { get; set; }
        public List<EventTrendItem> EventTrend { get; set; } = new();
        public List<RecentAlertItem> RecentAlerts { get; set; } = new();
        public List<TopUserItem> TopUsers { get; set; } = new();
        public List<TopFileItem> TopFiles { get; set; } = new();
        public SystemStatusItem SystemStatus { get; set; } = new();
    }

    public class EventTrendItem
    {
        public DateTime Date { get; set; }
        public int Count { get; set; }
    }

    public class RecentAlertItem
    {
        public int Id { get; set; }
        public string RuleName { get; set; } = string.Empty;
        public DateTime AlertTime { get; set; }
        public string Message { get; set; } = string.Empty;
        public bool IsProcessed { get; set; }
        public string FilePath { get; set; } = string.Empty;
        public string UserName { get; set; } = string.Empty;
    }

    public class TopUserItem
    {
        public string UserName { get; set; } = string.Empty;
        public int EventCount { get; set; }
    }

    public class TopFileItem
    {
        public string FilePath { get; set; } = string.Empty;
        public int AccessCount { get; set; }
    }

    public class SystemStatusItem
    {
        public string DatabaseStatus { get; set; } = string.Empty;
        public string ServiceStatus { get; set; } = string.Empty;
        public DateTime LastUpdateTime { get; set; }
    }

    // 日志相关视图模型
    public class LogListViewModel
    {
        public List<FileAccessLog> Logs { get; set; } = new();
        public LogFilterViewModel Filter { get; set; } = new();
        public int TotalCount { get; set; }
        public int TotalPages { get; set; }
    }

    public class LogFilterViewModel
    {
        public string? FilePath { get; set; }
        public string? UserName { get; set; }
        public string? Action { get; set; }
        public DateTime? StartDate { get; set; }
        public DateTime? EndDate { get; set; }
        public int Page { get; set; } = 1;
        public int PageSize { get; set; } = 20;
    }

    public class LogSearchRequest
    {
        public string SearchTerm { get; set; } = string.Empty;
        public int Limit { get; set; } = 50;
    }

    // 告警相关视图模型
    public class AlertListViewModel
    {
        public List<AlertHistory> Alerts { get; set; } = new();
        public AlertFilterViewModel Filter { get; set; } = new();
        public int TotalCount { get; set; }
        public int TotalPages { get; set; }
    }

    public class AlertFilterViewModel
    {
        public string? RuleName { get; set; }
        public bool? IsProcessed { get; set; }
        public DateTime? StartDate { get; set; }
        public DateTime? EndDate { get; set; }
        public int Page { get; set; } = 1;
        public int PageSize { get; set; } = 20;
    }

    public class AlertStatistics
    {
        public int TotalAlerts { get; set; }
        public int ProcessedAlerts { get; set; }
        public int UnprocessedAlerts { get; set; }
        public List<AlertByRuleItem> AlertsByRule { get; set; } = new();
        public List<AlertTrendItem> AlertTrend { get; set; } = new();
    }

    public class AlertByRuleItem
    {
        public string RuleName { get; set; } = string.Empty;
        public int Count { get; set; }
    }

    public class AlertTrendItem
    {
        public DateTime Date { get; set; }
        public int Count { get; set; }
    }

    // 系统管理相关视图模型
    public class SystemStatusViewModel
    {
        public bool IsDatabaseConnected { get; set; }
        public bool IsServiceRunning { get; set; }
        public string ServiceStatus { get; set; } = string.Empty;
        public DateTime LastCheckTime { get; set; }
        public SystemPerformanceInfo Performance { get; set; } = new();
    }

    public class SystemPerformanceInfo
    {
        public long TotalMemoryUsage { get; set; }
        public double CpuUsage { get; set; }
        public long DiskUsage { get; set; }
        public int ActiveConnections { get; set; }
    }

    public class SystemConfigViewModel
    {
        [Display(Name = "数据库连接字符串")]
        [Required(ErrorMessage = "数据库连接字符串不能为空")]
        public string ConnectionString { get; set; } = string.Empty;

        [Display(Name = "监控路径")]
        public List<string> MonitorPaths { get; set; } = new();

        [Display(Name = "启用事件日志监控")]
        public bool EnableEventLogMonitoring { get; set; }

        [Display(Name = "启用文件系统监控")]
        public bool EnableFileSystemMonitoring { get; set; }

        [Display(Name = "批量处理大小")]
        [Range(1, 1000, ErrorMessage = "批量处理大小必须在1-1000之间")]
        public int BatchSize { get; set; }

        [Display(Name = "批量间隔（秒）")]
        [Range(1, 300, ErrorMessage = "批量间隔必须在1-300秒之间")]
        public int BatchIntervalSeconds { get; set; }

        [Display(Name = "日志保留天数")]
        [Range(1, 365, ErrorMessage = "日志保留天数必须在1-365天之间")]
        public int LogRetentionDays { get; set; }
    }

    public class RuleViewModel
    {
        public string Name { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public bool Enabled { get; set; }
        public string Level { get; set; } = string.Empty;
        public string PathPattern { get; set; } = string.Empty;
        public List<string> Actions { get; set; } = new();
        public string TimeRange { get; set; } = string.Empty;
    }

    public class OperationResult
    {
        public bool IsSuccess { get; set; }
        public string Message { get; set; } = string.Empty;
    }

    // 错误视图模型
    public class ErrorViewModel
    {
        public string? RequestId { get; set; }
        public bool ShowRequestId => !string.IsNullOrEmpty(RequestId);
    }
}
