using Microsoft.AspNetCore.Mvc;
using FileMonitor.Web.Services;
using FileMonitor.Web.Models;

namespace FileMonitor.Web.Controllers
{
    public class AlertsController : Controller
    {
        private readonly AlertService _alertService;
        private readonly ILogger<AlertsController> _logger;

        public AlertsController(AlertService alertService, ILogger<AlertsController> logger)
        {
            _alertService = alertService;
            _logger = logger;
        }

        public async Task<IActionResult> Index(AlertFilterViewModel filter)
        {
            try
            {
                var model = await _alertService.GetAlertsAsync(filter);
                return View(model);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取告警数据时发生错误");
                return View(new AlertListViewModel());
            }
        }

        [HttpGet]
        public async Task<IActionResult> Details(int id)
        {
            try
            {
                var alert = await _alertService.GetAlertByIdAsync(id);
                if (alert == null)
                {
                    return NotFound();
                }
                return View(alert);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取告警详情时发生错误，ID: {Id}", id);
                return NotFound();
            }
        }

        [HttpPost]
        public async Task<IActionResult> MarkAsProcessed(int id, string processedBy)
        {
            try
            {
                await _alertService.MarkAsProcessedAsync(id, processedBy);
                return Json(new { success = true });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "标记告警为已处理时发生错误，ID: {Id}", id);
                return Json(new { success = false, error = "操作失败" });
            }
        }

        [HttpGet]
        public async Task<IActionResult> Statistics()
        {
            try
            {
                var stats = await _alertService.GetAlertStatisticsAsync();
                return Json(stats);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取告警统计时发生错误");
                return Json(new { error = "获取统计数据失败" });
            }
        }
    }
}
