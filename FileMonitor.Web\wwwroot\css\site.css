/* 自定义样式 */
.border-left-primary {
    border-left: 0.25rem solid #4e73df !important;
}

.border-left-success {
    border-left: 0.25rem solid #1cc88a !important;
}

.border-left-info {
    border-left: 0.25rem solid #36b9cc !important;
}

.border-left-warning {
    border-left: 0.25rem solid #f6c23e !important;
}

.border-left-danger {
    border-left: 0.25rem solid #e74a3b !important;
}

.text-gray-300 {
    color: #dddfeb !important;
}

.text-gray-500 {
    color: #858796 !important;
}

.text-gray-800 {
    color: #5a5c69 !important;
}

.text-xs {
    font-size: 0.7rem;
}

.font-weight-bold {
    font-weight: 700 !important;
}

.shadow {
    box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15) !important;
}

.card {
    border: 1px solid #e3e6f0;
    border-radius: 0.35rem;
}

.card-header {
    background-color: #f8f9fc;
    border-bottom: 1px solid #e3e6f0;
}

/* 表格样式 */
.table-hover tbody tr:hover {
    background-color: rgba(0, 0, 0, 0.075);
}

/* 侧边栏样式 */
.sidebar .nav-link {
    color: #333;
    padding: 0.75rem 1rem;
    border-radius: 0.35rem;
    margin: 0.125rem 0.5rem;
}

.sidebar .nav-link:hover {
    background-color: #eaecf4;
    color: #5a5c69;
}

.sidebar .nav-link.active {
    background-color: #4e73df;
    color: white;
}

.sidebar .nav-link i {
    margin-right: 0.5rem;
    width: 1rem;
    text-align: center;
}

.sidebar-heading {
    font-size: 0.75rem;
    font-weight: 800;
    letter-spacing: 0.05rem;
}

/* 徽章样式 */
.badge {
    font-size: 0.75rem;
    font-weight: 500;
}

/* 进度条样式 */
.progress {
    background-color: #eaecf4;
}

/* 图表容器 */
.chart-area {
    position: relative;
    height: 300px;
}

/* 响应式调整 */
@media (max-width: 768px) {
    main {
        margin-left: 0;
    }
    
    .sidebar {
        position: static;
        height: auto;
        padding: 0;
    }
    
    .sidebar-sticky {
        height: auto;
    }
}

/* 加载动画 */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: #fff;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* 状态指示器 */
.status-indicator {
    display: inline-block;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    margin-right: 0.5rem;
}

.status-indicator.online {
    background-color: #1cc88a;
}

.status-indicator.offline {
    background-color: #e74a3b;
}

.status-indicator.warning {
    background-color: #f6c23e;
}

/* 工具提示 */
.tooltip-inner {
    max-width: 300px;
    text-align: left;
}

/* 自定义按钮 */
.btn-circle {
    width: 30px;
    height: 30px;
    padding: 6px 0px;
    border-radius: 15px;
    text-align: center;
    font-size: 12px;
    line-height: 1.428571429;
}

.btn-circle.btn-sm {
    width: 30px;
    height: 30px;
    padding: 6px 0px;
    border-radius: 15px;
    font-size: 10px;
}

.btn-circle.btn-lg {
    width: 50px;
    height: 50px;
    padding: 10px 16px;
    border-radius: 25px;
    font-size: 17px;
    line-height: 1.33;
}

/* 数据表格 */
.datatable-wrapper {
    overflow-x: auto;
}

.datatable-wrapper table {
    min-width: 800px;
}

/* 搜索高亮 */
.search-highlight {
    background-color: #fff3cd;
    padding: 0.1rem 0.2rem;
    border-radius: 0.2rem;
}

/* 错误页面 */
.error-page {
    text-align: center;
    padding: 3rem 0;
}

.error-page .error-code {
    font-size: 6rem;
    font-weight: 300;
    color: #e3e6f0;
}

.error-page .error-message {
    font-size: 1.5rem;
    color: #5a5c69;
    margin-bottom: 2rem;
}

/* 打印样式 */
@media print {
    .sidebar,
    .navbar,
    .btn,
    .pagination {
        display: none !important;
    }
    
    main {
        margin-left: 0 !important;
    }
    
    .card {
        border: 1px solid #000 !important;
        box-shadow: none !important;
    }
}
