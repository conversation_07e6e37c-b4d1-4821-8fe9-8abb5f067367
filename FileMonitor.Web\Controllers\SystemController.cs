using Microsoft.AspNetCore.Mvc;
using FileMonitor.Web.Services;
using FileMonitor.Web.Models;

namespace FileMonitor.Web.Controllers
{
    public class SystemController : Controller
    {
        private readonly SystemService _systemService;
        private readonly ILogger<SystemController> _logger;

        public SystemController(SystemService systemService, ILogger<SystemController> logger)
        {
            _systemService = systemService;
            _logger = logger;
        }

        public async Task<IActionResult> Index()
        {
            try
            {
                var model = await _systemService.GetSystemStatusAsync();
                return View(model);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取系统状态时发生错误");
                return View(new SystemStatusViewModel());
            }
        }

        [HttpGet]
        public async Task<IActionResult> Configuration()
        {
            try
            {
                var config = await _systemService.GetConfigurationAsync();
                return View(config);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取系统配置时发生错误");
                return View(new SystemConfigViewModel());
            }
        }

        [HttpPost]
        public async Task<IActionResult> Configuration(SystemConfigViewModel model)
        {
            if (!ModelState.IsValid)
            {
                return View(model);
            }

            try
            {
                await _systemService.UpdateConfigurationAsync(model);
                TempData["Success"] = "配置更新成功";
                return RedirectToAction(nameof(Configuration));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "更新系统配置时发生错误");
                ModelState.AddModelError("", "配置更新失败");
                return View(model);
            }
        }

        [HttpGet]
        public async Task<IActionResult> Rules()
        {
            try
            {
                var rules = await _systemService.GetRulesAsync();
                return View(rules);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取规则配置时发生错误");
                return View(new List<RuleViewModel>());
            }
        }

        [HttpPost]
        public async Task<IActionResult> TestConnection()
        {
            try
            {
                var result = await _systemService.TestDatabaseConnectionAsync();
                return Json(new { success = result.IsSuccess, message = result.Message });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "测试数据库连接时发生错误");
                return Json(new { success = false, message = "连接测试失败" });
            }
        }

        [HttpPost]
        public async Task<IActionResult> RestartService()
        {
            try
            {
                var result = await _systemService.RestartMonitorServiceAsync();
                return Json(new { success = result.IsSuccess, message = result.Message });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "重启服务时发生错误");
                return Json(new { success = false, message = "重启服务失败" });
            }
        }
    }
}
