﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>Exe</OutputType>
    <TargetFramework>net9.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>

    <!-- 应用程序信息 -->
    <AssemblyTitle>文件监控服务</AssemblyTitle>
    <AssemblyDescription>监控文件服务器访问的Windows服务</AssemblyDescription>
    <AssemblyCompany>文件监控系统</AssemblyCompany>
    <AssemblyProduct>FileMonitor System</AssemblyProduct>
    <AssemblyCopyright>Copyright © 2025</AssemblyCopyright>
    <AssemblyVersion>*******</AssemblyVersion>
    <FileVersion>*******</FileVersion>
    <ProductVersion>1.0.0</ProductVersion>

    <!-- 图标和资源 -->
    <ApplicationIcon>icon.ico</ApplicationIcon>
    <Win32Resource />

    <!-- 发布设置 -->
    <PublishSingleFile>true</PublishSingleFile>
    <SelfContained>false</SelfContained>
    <PublishReadyToRun>true</PublishReadyToRun>
    <IncludeNativeLibrariesForSelfExtract>true</IncludeNativeLibrariesForSelfExtract>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.Extensions.Hosting" Version="9.0.7" />
    <PackageReference Include="Microsoft.Extensions.Hosting.WindowsServices" Version="9.0.7" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\FileMonitor.Core\FileMonitor.Core.csproj" />
  </ItemGroup>

</Project>
