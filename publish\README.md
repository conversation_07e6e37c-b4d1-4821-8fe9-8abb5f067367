# 文件监控系统 - 部署包

## 📦 包含内容

此部署包包含了完整的文件监控系统，可以直接在 Windows Server 上部署和运行。

### 🗂️ 目录结构

```
publish/
├── FileMonitor.Service.exe     # 主服务程序
├── *.dll                       # 依赖库文件
├── appsettings.json            # 主配置文件
├── appsettings.Production.json # 生产环境配置
├── Database/                   # 数据库脚本
│   └── init_database.sql      # 数据库初始化脚本
├── Rules/                      # 规则配置
│   └── rules.yaml             # 监控规则配置文件
├── Scripts/                    # 部署脚本
│   ├── install-service.ps1    # 服务安装脚本
│   ├── uninstall-service.ps1  # 服务卸载脚本
│   ├── build-and-deploy.ps1   # 构建部署脚本
│   └── test-system.ps1        # 系统测试脚本
└── logs/                       # 日志目录
```

## 🚀 快速部署

### 1. 环境准备

- ✅ Windows Server 2019/2022 或 Windows 10/11
- ✅ .NET 8.0 Runtime 或更高版本
- ✅ MySQL 8.0+ 数据库
- ✅ 管理员权限

### 2. 数据库设置

1. 确保 MySQL 服务正在运行
2. 使用 MySQL 客户端执行数据库初始化：

```bash
mysql -u root -p < Database/init_database.sql
```

### 3. 配置系统

编辑 `appsettings.json` 文件，修改以下关键配置：

```json
{
  "ConnectionString": "Server=127.0.0.1;Database=FileMonitorDB;User=root;Password=YOUR_PASSWORD;",
  "MonitorPaths": [
    "C:\\SharedFiles",
    "C:\\ImportantDocuments"
  ],
  "Email": {
    "SmtpServer": "your-smtp-server.com",
    "FromEmail": "<EMAIL>",
    "ToEmails": ["<EMAIL>"]
  }
}
```

### 4. 安装服务

以管理员身份运行 PowerShell，执行：

```powershell
.\Scripts\install-service.ps1
```

### 5. 启动服务

```powershell
Start-Service -Name FileMonitorService
```

## 🔧 配置说明

### 主要配置项

| 配置项 | 说明 | 示例 |
|--------|------|------|
| ConnectionString | MySQL 连接字符串 | `Server=127.0.0.1;Database=FileMonitorDB;User=root;Password=******;` |
| MonitorPaths | 监控路径列表 | `["C:\\SharedFiles", "D:\\Data"]` |
| EnableEventLogMonitoring | 启用事件日志监控 | `true` |
| EnableFileSystemMonitoring | 启用文件系统监控 | `true` |

### 规则配置

编辑 `Rules/rules.yaml` 文件可以自定义监控规则。系统已预置了以下规则：

- 🔴 敏感文件访问告警
- 🟡 非工作时间访问
- 🔴 批量删除操作告警
- 🟠 外部IP访问告警
- 🟡 大文件操作告警
- 🟠 可执行文件访问告警
- 🔴 系统文件访问告警

## 📊 监控能力

### 文件操作检测
- ✅ 文件创建 (Create)
- ✅ 文件读取 (Read)
- ✅ 文件写入 (Write)
- ✅ 文件删除 (Delete)
- ✅ 文件重命名 (Rename)
- ✅ 权限变更 (Security)

### 告警方式
- 📧 邮件通知
- 📝 Windows 事件日志
- 🗄️ 数据库记录

## 🛠️ 管理命令

### 服务管理
```powershell
# 查看服务状态
Get-Service -Name FileMonitorService

# 启动服务
Start-Service -Name FileMonitorService

# 停止服务
Stop-Service -Name FileMonitorService

# 重启服务
Restart-Service -Name FileMonitorService
```

### 日志查看
```powershell
# 查看 Windows 事件日志
Get-EventLog -LogName Application -Source FileMonitorService -Newest 10

# 查看应用程序日志
Get-Content "logs\filemonitor.log" -Tail 50
```

### 数据库查询
```sql
-- 查看最近的文件访问记录
SELECT * FROM access_log ORDER BY event_time DESC LIMIT 20;

-- 查看告警记录
SELECT * FROM alert_history ORDER BY alert_time DESC LIMIT 10;

-- 查看今日统计
SELECT COUNT(*) as total_events, 
       COUNT(DISTINCT user_name) as unique_users,
       COUNT(DISTINCT file_path) as unique_files
FROM access_log 
WHERE DATE(event_time) = CURDATE();
```

## 🧪 测试系统

运行测试脚本验证系统功能：

```powershell
.\Scripts\test-system.ps1
```

## 🔍 故障排除

### 常见问题

1. **服务无法启动**
   - 检查数据库连接配置
   - 验证 MySQL 服务状态
   - 确认服务账户权限

2. **监控无数据**
   - 检查监控路径是否存在
   - 验证文件系统权限
   - 确认规则配置正确

3. **告警不发送**
   - 检查邮件服务器配置
   - 验证网络连接
   - 查看错误日志

### 性能调优

编辑配置文件中的 Performance 部分：

```json
{
  "Performance": {
    "BatchSize": 200,           // 批量处理大小
    "BatchIntervalSeconds": 15, // 批量间隔（秒）
    "MaxCacheSize": 2000,       // 最大缓存大小
    "LogRetentionDays": 180     // 日志保留天数
  }
}
```

## 📞 技术支持

如遇到问题，请：

1. 查看 Windows 事件日志
2. 检查应用程序日志文件
3. 验证数据库连接和数据
4. 运行测试脚本诊断

## 🔄 卸载系统

如需卸载系统：

```powershell
.\Scripts\uninstall-service.ps1
```

---

**版本**: v1.0.0  
**构建时间**: 2025-07-18  
**支持平台**: Windows Server 2019+, Windows 10+
