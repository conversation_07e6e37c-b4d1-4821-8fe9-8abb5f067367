# 文件监控系统构建和部署脚本

param(
    [string]$Configuration = "Release",
    [string]$OutputPath = "",
    [switch]$InstallService = $false,
    [switch]$StartService = $false,
    [switch]$SkipTests = $false
)

$ErrorActionPreference = "Stop"

# 获取脚本目录和项目根目录
$ScriptDir = Split-Path -Parent $MyInvocation.MyCommand.Definition
$ProjectRoot = Split-Path -Parent $ScriptDir

# 设置默认输出路径
if ([string]::IsNullOrEmpty($OutputPath)) {
    $OutputPath = Join-Path $ProjectRoot "publish"
}

Write-Host "=== 文件监控系统构建和部署脚本 ===" -ForegroundColor Green
Write-Host "项目根目录: $ProjectRoot"
Write-Host "配置: $Configuration"
Write-Host "输出路径: $OutputPath"
Write-Host ""

try {
    # 切换到项目根目录
    Set-Location $ProjectRoot
    
    # 清理之前的构建
    Write-Host "清理之前的构建..." -ForegroundColor Yellow
    if (Test-Path $OutputPath) {
        Remove-Item $OutputPath -Recurse -Force
    }
    
    # 还原 NuGet 包
    Write-Host "还原 NuGet 包..." -ForegroundColor Yellow
    dotnet restore
    if ($LASTEXITCODE -ne 0) {
        throw "NuGet 包还原失败"
    }
    
    # 运行测试（如果未跳过）
    if (-not $SkipTests) {
        Write-Host "运行单元测试..." -ForegroundColor Yellow
        # 这里可以添加测试项目
        # dotnet test --configuration $Configuration --no-restore
        Write-Host "跳过测试（暂无测试项目）" -ForegroundColor Gray
    }
    
    # 构建解决方案
    Write-Host "构建解决方案..." -ForegroundColor Yellow
    dotnet build --configuration $Configuration --no-restore
    if ($LASTEXITCODE -ne 0) {
        throw "构建失败"
    }
    
    # 发布服务项目
    Write-Host "发布服务项目..." -ForegroundColor Yellow
    $publishArgs = @(
        "publish"
        "FileMonitor.Service"
        "--configuration", $Configuration
        "--output", $OutputPath
        "--no-build"
        "--self-contained", "false"
    )
    
    & dotnet @publishArgs
    if ($LASTEXITCODE -ne 0) {
        throw "发布失败"
    }
    
    # 复制配置文件
    Write-Host "复制配置文件..." -ForegroundColor Yellow
    
    # 复制规则配置
    $rulesSource = Join-Path $ProjectRoot "Rules"
    $rulesTarget = Join-Path $OutputPath "Rules"
    if (Test-Path $rulesSource) {
        Copy-Item $rulesSource $rulesTarget -Recurse -Force
    }
    
    # 复制数据库脚本
    $dbSource = Join-Path $ProjectRoot "Database"
    $dbTarget = Join-Path $OutputPath "Database"
    if (Test-Path $dbSource) {
        Copy-Item $dbSource $dbTarget -Recurse -Force
    }
    
    # 复制部署脚本
    $scriptsSource = Join-Path $ProjectRoot "Scripts"
    $scriptsTarget = Join-Path $OutputPath "Scripts"
    if (Test-Path $scriptsSource) {
        Copy-Item $scriptsSource $scriptsTarget -Recurse -Force
    }
    
    # 创建日志目录
    $logsDir = Join-Path $OutputPath "logs"
    if (-not (Test-Path $logsDir)) {
        New-Item -ItemType Directory -Path $logsDir -Force | Out-Null
    }
    
    Write-Host "构建完成！" -ForegroundColor Green
    Write-Host "发布路径: $OutputPath"
    
    # 显示发布的文件
    Write-Host ""
    Write-Host "发布的文件:" -ForegroundColor Cyan
    Get-ChildItem $OutputPath -Recurse | Where-Object { -not $_.PSIsContainer } | 
        Select-Object FullName | ForEach-Object { 
            $relativePath = $_.FullName.Substring($OutputPath.Length + 1)
            Write-Host "  $relativePath" -ForegroundColor Gray
        }
    
    # 安装服务（如果指定）
    if ($InstallService) {
        Write-Host ""
        Write-Host "安装服务..." -ForegroundColor Yellow
        
        $installScript = Join-Path $OutputPath "Scripts\install-service.ps1"
        $binaryPath = Join-Path $OutputPath "FileMonitor.Service.exe"
        
        if (Test-Path $installScript) {
            & $installScript -BinaryPath $binaryPath
            
            if ($StartService) {
                Write-Host "启动服务..." -ForegroundColor Yellow
                Start-Service -Name "FileMonitorService"
            }
        } else {
            Write-Warning "找不到安装脚本: $installScript"
        }
    }
    
} catch {
    Write-Error "构建过程中发生错误: $($_.Exception.Message)"
    exit 1
} finally {
    # 恢复原始位置
    Set-Location $ScriptDir
}

Write-Host ""
Write-Host "=== 构建和部署完成 ===" -ForegroundColor Green
Write-Host ""
Write-Host "下一步操作:"
Write-Host "1. 检查并修改配置文件: $(Join-Path $OutputPath 'appsettings.json')"
Write-Host "2. 检查并修改规则配置: $(Join-Path $OutputPath 'Rules\rules.yaml')"
Write-Host "3. 运行数据库初始化脚本: $(Join-Path $OutputPath 'Database\init_database.sql')"
if (-not $InstallService) {
    Write-Host "4. 安装服务: $(Join-Path $OutputPath 'Scripts\install-service.ps1')"
}
Write-Host ""
Write-Host "服务管理命令:"
Write-Host "  查看服务状态: Get-Service -Name FileMonitorService"
Write-Host "  启动服务: Start-Service -Name FileMonitorService"
Write-Host "  停止服务: Stop-Service -Name FileMonitorService"
Write-Host "  查看日志: Get-EventLog -LogName Application -Source FileMonitorService -Newest 10"
