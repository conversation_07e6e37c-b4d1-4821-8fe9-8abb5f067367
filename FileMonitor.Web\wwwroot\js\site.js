// 全局 JavaScript 功能

// 页面加载完成后执行
document.addEventListener('DOMContentLoaded', function() {
    // 初始化工具提示
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });

    // 初始化弹出框
    var popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'));
    var popoverList = popoverTriggerList.map(function (popoverTriggerEl) {
        return new bootstrap.Popover(popoverTriggerEl);
    });

    // 自动隐藏警告消息
    setTimeout(function() {
        var alerts = document.querySelectorAll('.alert');
        alerts.forEach(function(alert) {
            var bsAlert = new bootstrap.Alert(alert);
            bsAlert.close();
        });
    }, 5000);
});

// 通用工具函数
const Utils = {
    // 格式化日期
    formatDate: function(dateString, format = 'YYYY-MM-DD HH:mm:ss') {
        const date = new Date(dateString);
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const day = String(date.getDate()).padStart(2, '0');
        const hours = String(date.getHours()).padStart(2, '0');
        const minutes = String(date.getMinutes()).padStart(2, '0');
        const seconds = String(date.getSeconds()).padStart(2, '0');

        return format
            .replace('YYYY', year)
            .replace('MM', month)
            .replace('DD', day)
            .replace('HH', hours)
            .replace('mm', minutes)
            .replace('ss', seconds);
    },

    // 格式化文件大小
    formatFileSize: function(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    },

    // 显示加载状态
    showLoading: function(element) {
        const originalContent = element.innerHTML;
        element.innerHTML = '<span class="loading"></span> 加载中...';
        element.disabled = true;
        return originalContent;
    },

    // 隐藏加载状态
    hideLoading: function(element, originalContent) {
        element.innerHTML = originalContent;
        element.disabled = false;
    },

    // 显示成功消息
    showSuccess: function(message) {
        this.showAlert(message, 'success');
    },

    // 显示错误消息
    showError: function(message) {
        this.showAlert(message, 'danger');
    },

    // 显示警告消息
    showWarning: function(message) {
        this.showAlert(message, 'warning');
    },

    // 显示信息消息
    showInfo: function(message) {
        this.showAlert(message, 'info');
    },

    // 显示警告框
    showAlert: function(message, type = 'info') {
        const alertHtml = `
            <div class="alert alert-${type} alert-dismissible fade show" role="alert">
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        `;
        
        // 查找或创建警告容器
        let alertContainer = document.getElementById('alert-container');
        if (!alertContainer) {
            alertContainer = document.createElement('div');
            alertContainer.id = 'alert-container';
            alertContainer.className = 'position-fixed top-0 end-0 p-3';
            alertContainer.style.zIndex = '9999';
            document.body.appendChild(alertContainer);
        }
        
        alertContainer.insertAdjacentHTML('beforeend', alertHtml);
        
        // 自动隐藏
        setTimeout(function() {
            const alerts = alertContainer.querySelectorAll('.alert');
            if (alerts.length > 0) {
                const bsAlert = new bootstrap.Alert(alerts[alerts.length - 1]);
                bsAlert.close();
            }
        }, 5000);
    },

    // 确认对话框
    confirm: function(message, callback) {
        if (confirm(message)) {
            callback();
        }
    },

    // AJAX 请求封装
    ajax: function(options) {
        const defaults = {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json',
            },
            credentials: 'same-origin'
        };

        const config = Object.assign({}, defaults, options);

        return fetch(config.url, config)
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                return response.json();
            })
            .catch(error => {
                console.error('AJAX request failed:', error);
                this.showError('请求失败: ' + error.message);
                throw error;
            });
    }
};

// 数据表格功能
const DataTable = {
    // 初始化排序
    initSort: function(tableId) {
        const table = document.getElementById(tableId);
        if (!table) return;

        const headers = table.querySelectorAll('th[data-sort]');
        headers.forEach(header => {
            header.style.cursor = 'pointer';
            header.addEventListener('click', function() {
                const column = this.dataset.sort;
                const currentOrder = this.dataset.order || 'asc';
                const newOrder = currentOrder === 'asc' ? 'desc' : 'asc';
                
                // 更新排序指示器
                headers.forEach(h => {
                    h.classList.remove('sort-asc', 'sort-desc');
                    delete h.dataset.order;
                });
                
                this.dataset.order = newOrder;
                this.classList.add('sort-' + newOrder);
                
                // 执行排序
                this.sortTable(table, column, newOrder);
            });
        });
    },

    // 表格排序
    sortTable: function(table, column, order) {
        const tbody = table.querySelector('tbody');
        const rows = Array.from(tbody.querySelectorAll('tr'));
        
        rows.sort((a, b) => {
            const aValue = a.querySelector(`[data-value="${column}"]`)?.textContent || '';
            const bValue = b.querySelector(`[data-value="${column}"]`)?.textContent || '';
            
            if (order === 'asc') {
                return aValue.localeCompare(bValue);
            } else {
                return bValue.localeCompare(aValue);
            }
        });
        
        // 重新插入排序后的行
        rows.forEach(row => tbody.appendChild(row));
    },

    // 表格搜索
    initSearch: function(searchInputId, tableId) {
        const searchInput = document.getElementById(searchInputId);
        const table = document.getElementById(tableId);
        
        if (!searchInput || !table) return;
        
        searchInput.addEventListener('input', function() {
            const searchTerm = this.value.toLowerCase();
            const rows = table.querySelectorAll('tbody tr');
            
            rows.forEach(row => {
                const text = row.textContent.toLowerCase();
                if (text.includes(searchTerm)) {
                    row.style.display = '';
                } else {
                    row.style.display = 'none';
                }
            });
        });
    }
};

// 图表功能
const Charts = {
    // 默认颜色主题
    colors: {
        primary: '#4e73df',
        success: '#1cc88a',
        info: '#36b9cc',
        warning: '#f6c23e',
        danger: '#e74a3b',
        secondary: '#858796'
    },

    // 创建线图
    createLineChart: function(canvasId, data, options = {}) {
        const ctx = document.getElementById(canvasId);
        if (!ctx) return null;

        const defaultOptions = {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                }
            },
            scales: {
                x: {
                    grid: {
                        display: false
                    }
                },
                y: {
                    beginAtZero: true,
                    grid: {
                        color: '#eaecf4'
                    }
                }
            }
        };

        return new Chart(ctx, {
            type: 'line',
            data: data,
            options: Object.assign({}, defaultOptions, options)
        });
    },

    // 创建饼图
    createPieChart: function(canvasId, data, options = {}) {
        const ctx = document.getElementById(canvasId);
        if (!ctx) return null;

        const defaultOptions = {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom'
                }
            }
        };

        return new Chart(ctx, {
            type: 'pie',
            data: data,
            options: Object.assign({}, defaultOptions, options)
        });
    }
};

// 实时更新功能
const RealTime = {
    intervals: {},

    // 开始实时更新
    start: function(key, callback, interval = 30000) {
        this.stop(key); // 先停止已存在的定时器
        this.intervals[key] = setInterval(callback, interval);
        callback(); // 立即执行一次
    },

    // 停止实时更新
    stop: function(key) {
        if (this.intervals[key]) {
            clearInterval(this.intervals[key]);
            delete this.intervals[key];
        }
    },

    // 停止所有实时更新
    stopAll: function() {
        Object.keys(this.intervals).forEach(key => {
            this.stop(key);
        });
    }
};

// 页面卸载时清理定时器
window.addEventListener('beforeunload', function() {
    RealTime.stopAll();
});

// 导出到全局
window.Utils = Utils;
window.DataTable = DataTable;
window.Charts = Charts;
window.RealTime = RealTime;
