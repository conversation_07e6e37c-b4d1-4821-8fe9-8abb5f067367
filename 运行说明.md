# 文件监控系统 - 运行说明

## 🚀 快速启动

### 方式一：一键启动（推荐）

#### Windows 批处理文件
```bash
# 双击运行
start-system.bat
```

#### PowerShell 脚本
```powershell
# 右键 -> 使用 PowerShell 运行
.\start-system.ps1

# 或指定端口
.\start-system.ps1 -WebPort 8080

# 测试模式（先编译再运行）
.\start-system.ps1 -TestMode
```

### 方式二：手动启动

#### 1. 启动 Web 管理界面
```bash
cd FileMonitor.Web
dotnet run --urls "http://localhost:5001"
```

#### 2. 启动监控服务（可选）
```bash
# 开发模式
cd FileMonitor.Service
dotnet run

# 或运行已发布版本
.\publish\FileMonitor.Service.exe
```

## 📋 运行前准备

### 1. 环境要求
- ✅ Windows 10/11 或 Windows Server 2019+
- ✅ .NET 8.0 Runtime 或更高版本
- ✅ MySQL 8.0+ 数据库服务

### 2. 数据库初始化

#### 自动初始化
```bash
# 双击运行
init-database.bat
```

#### 手动初始化
```sql
-- 使用 MySQL 客户端执行
mysql -u root -p < Database\init_database.sql
```

### 3. 配置检查

检查配置文件 `FileMonitor.Web\appsettings.json`：
```json
{
  "ConnectionString": "Server=127.0.0.1;Database=FileMonitorDB;User=root;Password=******;",
  "MonitorPaths": [
    "C:\\SharedFiles",
    "C:\\ImportantDocuments"
  ]
}
```

## 🌐 访问系统

### Web 管理界面
- **默认地址**: http://localhost:5001
- **功能模块**:
  - 📊 **仪表板**: 系统概览和实时统计
  - 📋 **访问日志**: 查看和搜索文件访问记录
  - 🚨 **告警管理**: 查看和处理安全告警
  - ⚙️ **系统管理**: 配置和监控系统状态

### 首次使用
1. 打开浏览器访问 http://localhost:5001
2. 查看仪表板了解系统状态
3. 在系统管理中测试数据库连接
4. 根据需要修改监控规则

## ⚙️ 配置说明

### 监控路径配置
编辑 `appsettings.json` 中的 `MonitorPaths`：
```json
"MonitorPaths": [
  "C:\\SharedFiles",
  "C:\\ImportantDocuments",
  "\\\\server\\share"
]
```

### 监控规则配置
编辑 `Rules\rules.yaml` 文件来自定义监控规则：
```yaml
rules:
  - name: "敏感文件访问告警"
    enabled: true
    level: High
    conditions:
      pathPattern: ".*[机密|保密].*\\.(xlsx?|docx?|pdf)$"
      actions: ["Read", "Write", "Delete"]
```

### 邮件告警配置
在 `appsettings.json` 中配置邮件设置：
```json
"Email": {
  "SmtpServer": "smtp.company.com",
  "FromEmail": "<EMAIL>",
  "ToEmails": ["<EMAIL>"]
}
```

## 🔧 常见问题

### 1. 应用无法启动
**问题**: 端口被占用或权限不足
**解决**:
```bash
# 检查端口占用
netstat -ano | findstr :5001

# 使用其他端口
.\start-system.ps1 -WebPort 8080
```

### 2. 数据库连接失败
**问题**: MySQL 服务未启动或配置错误
**解决**:
```bash
# 检查 MySQL 服务
net start mysql

# 测试连接
mysql -h 127.0.0.1 -u root -p****** -e "SELECT 1"
```

### 3. 监控无数据
**问题**: 监控路径不存在或权限不足
**解决**:
- 检查监控路径是否存在
- 确保应用有读取权限
- 查看系统管理页面的状态信息

### 4. 告警不发送
**问题**: 邮件配置错误
**解决**:
- 检查 SMTP 服务器配置
- 验证邮箱账户和密码
- 在系统管理中测试邮件配置

## 📊 系统监控

### 实时状态查看
- **仪表板**: 查看今日统计和趋势图表
- **系统状态**: 监控数据库连接和服务运行状态
- **告警徽章**: 实时显示未处理告警数量

### 日志查看
- **应用日志**: `logs\filemonitor.log`
- **系统日志**: Windows 事件查看器
- **数据库日志**: 查询 `access_log` 表

## 🛑 停止系统

### 停止 Web 应用
- 在命令行窗口按 `Ctrl+C`
- 或关闭命令行窗口

### 停止监控服务
```bash
# 如果作为服务运行
net stop FileMonitorService

# 或直接结束进程
taskkill /f /im FileMonitor.Service.exe
```

## 📦 部署到生产环境

### 1. 编译发布版本
```bash
.\Scripts\build-and-deploy.ps1 -Configuration Release
```

### 2. 安装为 Windows 服务
```bash
.\Scripts\install-service.ps1
```

### 3. 配置 IIS（Web UI）
```bash
.\Scripts\deploy-web-ui.ps1 -Configuration Release -Port 80
```

## 📞 技术支持

如遇到问题，请：
1. 查看应用日志文件
2. 检查系统事件日志
3. 验证数据库连接和数据
4. 参考本文档的故障排除部分

---

**版本**: v1.0.0  
**更新时间**: 2025-07-18  
**技术支持**: 文件监控系统开发团队
