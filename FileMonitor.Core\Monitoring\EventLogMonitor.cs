using System;
using System.Collections.Generic;
using System.Diagnostics.Eventing.Reader;
using System.Threading;
using System.Threading.Tasks;
using FileMonitor.Core.Models;
using FileMonitor.Core.Database;
using Microsoft.Extensions.Logging;

namespace FileMonitor.Core.Monitoring
{
    /// <summary>
    /// Windows事件日志监控器
    /// </summary>
    public class EventLogMonitor : IDisposable
    {
        private readonly DatabaseService _databaseService;
        private readonly ILogger<EventLogMonitor> _logger;
        private EventLogWatcher? _eventWatcher;
        private CancellationTokenSource? _cancellationTokenSource;
        private bool _disposed = false;

        public event EventHandler<FileAccessLog>? FileAccessDetected;

        public EventLogMonitor(DatabaseService databaseService, ILogger<EventLogMonitor> logger)
        {
            _databaseService = databaseService ?? throw new ArgumentNullException(nameof(databaseService));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }

        /// <summary>
        /// 开始监控Windows安全事件日志
        /// </summary>
        public void StartMonitoring()
        {
            try
            {
                _cancellationTokenSource = new CancellationTokenSource();

                // 创建事件查询，监控文件访问相关的事件ID
                // 4663: 尝试访问对象
                // 4656: 请求对象句柄
                // 4658: 关闭对象句柄
                var query = "*[System[(EventID=4663 or EventID=4656 or EventID=4658)]]";
                var eventQuery = new EventLogQuery("Security", PathType.LogName, query);

                _eventWatcher = new EventLogWatcher(eventQuery);
                _eventWatcher.EventRecordWritten += OnEventRecordWritten;
                _eventWatcher.Enabled = true;

                _logger.LogInformation("Windows事件日志监控已启动");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "启动Windows事件日志监控失败");
                throw;
            }
        }

        /// <summary>
        /// 停止监控
        /// </summary>
        public void StopMonitoring()
        {
            try
            {
                _cancellationTokenSource?.Cancel();
                
                if (_eventWatcher != null)
                {
                    _eventWatcher.Enabled = false;
                    _eventWatcher.Dispose();
                    _eventWatcher = null;
                }

                _logger.LogInformation("Windows事件日志监控已停止");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "停止Windows事件日志监控时发生错误");
            }
        }

        /// <summary>
        /// 处理事件记录
        /// </summary>
        private void OnEventRecordWritten(object? sender, EventRecordWrittenEventArgs e)
        {
            if (e.EventRecord == null)
                return;

            try
            {
                var log = ParseEventRecord(e.EventRecord);
                if (log != null)
                {
                    ProcessFileAccessLog(log);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "处理事件记录时发生错误");
            }
        }

        /// <summary>
        /// 解析事件记录
        /// </summary>
        private FileAccessLog? ParseEventRecord(EventRecord eventRecord)
        {
            try
            {
                var eventId = eventRecord.Id;
                var properties = eventRecord.Properties;

                // 根据不同的事件ID解析不同的字段
                switch (eventId)
                {
                    case 4663: // 尝试访问对象
                        return ParseEvent4663(eventRecord, properties);
                    case 4656: // 请求对象句柄
                        return ParseEvent4656(eventRecord, properties);
                    case 4658: // 关闭对象句柄
                        return ParseEvent4658(eventRecord, properties);
                    default:
                        return null;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "解析事件记录失败，EventID: {EventId}", eventRecord.Id);
                return null;
            }
        }

        /// <summary>
        /// 解析事件ID 4663 (尝试访问对象)
        /// </summary>
        private FileAccessLog? ParseEvent4663(EventRecord eventRecord, IList<EventProperty> properties)
        {
            if (properties.Count < 12)
                return null;

            var objectName = properties[6]?.Value?.ToString();
            if (string.IsNullOrEmpty(objectName) || !IsFileObject(objectName))
                return null;

            var subjectUserName = properties[1]?.Value?.ToString();
            var subjectDomainName = properties[2]?.Value?.ToString();
            var accessMask = properties[9]?.Value?.ToString();
            var accessList = properties[11]?.Value?.ToString();

            return new FileAccessLog
            {
                EventTime = eventRecord.TimeCreated ?? DateTime.Now,
                FilePath = objectName,
                Action = DetermineActionFromAccessMask(accessMask, accessList),
                UserName = $"{subjectDomainName}\\{subjectUserName}",
                SourceIP = GetSourceIPFromEvent(eventRecord),
                Details = $"EventID: 4663, AccessMask: {accessMask}, AccessList: {accessList}"
            };
        }

        /// <summary>
        /// 解析事件ID 4656 (请求对象句柄)
        /// </summary>
        private FileAccessLog? ParseEvent4656(EventRecord eventRecord, IList<EventProperty> properties)
        {
            if (properties.Count < 11)
                return null;

            var objectName = properties[6]?.Value?.ToString();
            if (string.IsNullOrEmpty(objectName) || !IsFileObject(objectName))
                return null;

            var subjectUserName = properties[1]?.Value?.ToString();
            var subjectDomainName = properties[2]?.Value?.ToString();
            var desiredAccess = properties[10]?.Value?.ToString();

            return new FileAccessLog
            {
                EventTime = eventRecord.TimeCreated ?? DateTime.Now,
                FilePath = objectName,
                Action = "HandleRequest",
                UserName = $"{subjectDomainName}\\{subjectUserName}",
                SourceIP = GetSourceIPFromEvent(eventRecord),
                Details = $"EventID: 4656, DesiredAccess: {desiredAccess}"
            };
        }

        /// <summary>
        /// 解析事件ID 4658 (关闭对象句柄)
        /// </summary>
        private FileAccessLog? ParseEvent4658(EventRecord eventRecord, IList<EventProperty> properties)
        {
            if (properties.Count < 8)
                return null;

            var objectName = properties[6]?.Value?.ToString();
            if (string.IsNullOrEmpty(objectName) || !IsFileObject(objectName))
                return null;

            var subjectUserName = properties[1]?.Value?.ToString();
            var subjectDomainName = properties[2]?.Value?.ToString();

            return new FileAccessLog
            {
                EventTime = eventRecord.TimeCreated ?? DateTime.Now,
                FilePath = objectName,
                Action = "HandleClose",
                UserName = $"{subjectDomainName}\\{subjectUserName}",
                SourceIP = GetSourceIPFromEvent(eventRecord),
                Details = $"EventID: 4658"
            };
        }

        /// <summary>
        /// 判断是否为文件对象
        /// </summary>
        private bool IsFileObject(string objectName)
        {
            // 过滤掉非文件对象，如注册表、进程等
            return objectName.Contains("\\") && 
                   !objectName.StartsWith("\\Registry") &&
                   !objectName.StartsWith("\\Device\\") &&
                   !objectName.Contains("\\BaseNamedObjects\\");
        }

        /// <summary>
        /// 根据访问掩码确定操作类型
        /// </summary>
        private string DetermineActionFromAccessMask(string? accessMask, string? accessList)
        {
            if (string.IsNullOrEmpty(accessList))
                return "Unknown";

            var actions = new List<string>();

            if (accessList.Contains("ReadData") || accessList.Contains("READ_CONTROL"))
                actions.Add("Read");
            
            if (accessList.Contains("WriteData") || accessList.Contains("AppendData"))
                actions.Add("Write");
            
            if (accessList.Contains("DELETE"))
                actions.Add("Delete");

            return actions.Count > 0 ? string.Join(",", actions) : "Access";
        }

        /// <summary>
        /// 从事件中获取源IP地址
        /// </summary>
        private string GetSourceIPFromEvent(EventRecord eventRecord)
        {
            try
            {
                // 尝试从事件的其他属性中获取IP地址
                // 这里可能需要根据具体的事件结构进行调整
                return "127.0.0.1"; // 默认本地IP
            }
            catch
            {
                return "127.0.0.1";
            }
        }

        /// <summary>
        /// 处理文件访问日志
        /// </summary>
        private void ProcessFileAccessLog(FileAccessLog log)
        {
            // 触发事件
            FileAccessDetected?.Invoke(this, log);

            // 异步保存到数据库
            Task.Run(async () =>
            {
                try
                {
                    await _databaseService.SaveLogAsync(log);
                    _logger.LogDebug("事件日志已保存: {FilePath} - {Action}", log.FilePath, log.Action);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "保存事件日志失败: {FilePath}", log.FilePath);
                }
            });
        }

        public void Dispose()
        {
            if (!_disposed)
            {
                StopMonitoring();
                _cancellationTokenSource?.Dispose();
                _disposed = true;
            }
        }
    }
}
