using FileMonitor.Core.Configuration;
using FileMonitor.Core.Database;
using FileMonitor.Web.Models;
using System.Diagnostics;
using System.ServiceProcess;
using YamlDotNet.Serialization;

namespace FileMonitor.Web.Services
{
    public class SystemService
    {
        private readonly DatabaseService _databaseService;
        private readonly AppConfig _appConfig;
        private readonly ILogger<SystemService> _logger;

        public SystemService(DatabaseService databaseService, AppConfig appConfig, ILogger<SystemService> logger)
        {
            _databaseService = databaseService;
            _appConfig = appConfig;
            _logger = logger;
        }

        public async Task<SystemStatusViewModel> GetSystemStatusAsync()
        {
            var model = new SystemStatusViewModel
            {
                LastCheckTime = DateTime.Now
            };

            try
            {
                // 检查数据库连接
                model.IsDatabaseConnected = await _databaseService.TestConnectionAsync();

                // 检查服务状态
                model.IsServiceRunning = IsServiceRunning("FileMonitorService");
                model.ServiceStatus = model.IsServiceRunning ? "运行中" : "已停止";

                // 获取性能信息
                model.Performance = GetPerformanceInfo();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取系统状态时发生错误");
            }

            return model;
        }

        public async Task<SystemConfigViewModel> GetConfigurationAsync()
        {
            return new SystemConfigViewModel
            {
                ConnectionString = _appConfig.ConnectionString,
                MonitorPaths = _appConfig.MonitorPaths,
                EnableEventLogMonitoring = _appConfig.EnableEventLogMonitoring,
                EnableFileSystemMonitoring = _appConfig.EnableFileSystemMonitoring,
                BatchSize = _appConfig.Performance.BatchSize,
                BatchIntervalSeconds = _appConfig.Performance.BatchIntervalSeconds,
                LogRetentionDays = _appConfig.Performance.LogRetentionDays
            };
        }

        public async Task UpdateConfigurationAsync(SystemConfigViewModel model)
        {
            // 这里应该更新配置文件
            // 由于这是一个演示，我们只记录日志
            _logger.LogInformation("配置更新请求: {@Config}", model);
            
            // 在实际应用中，这里应该：
            // 1. 更新 appsettings.json 文件
            // 2. 重新加载配置
            // 3. 可能需要重启服务
            
            await Task.CompletedTask;
        }

        public async Task<List<RuleViewModel>> GetRulesAsync()
        {
            try
            {
                if (!File.Exists(_appConfig.RulesConfigPath))
                {
                    return new List<RuleViewModel>();
                }

                var yamlContent = await File.ReadAllTextAsync(_appConfig.RulesConfigPath);
                var deserializer = new DeserializerBuilder().Build();
                var rulesConfig = deserializer.Deserialize<dynamic>(yamlContent);

                var rules = new List<RuleViewModel>();
                
                // 这里需要根据实际的 YAML 结构来解析
                // 这是一个简化的示例
                
                return rules;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取规则配置时发生错误");
                return new List<RuleViewModel>();
            }
        }

        public async Task<OperationResult> TestDatabaseConnectionAsync()
        {
            try
            {
                var isConnected = await _databaseService.TestConnectionAsync();
                return new OperationResult
                {
                    IsSuccess = isConnected,
                    Message = isConnected ? "数据库连接正常" : "数据库连接失败"
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "测试数据库连接时发生错误");
                return new OperationResult
                {
                    IsSuccess = false,
                    Message = $"连接测试失败: {ex.Message}"
                };
            }
        }

        public async Task<OperationResult> RestartMonitorServiceAsync()
        {
            try
            {
                using var service = new ServiceController("FileMonitorService");
                
                if (service.Status == ServiceControllerStatus.Running)
                {
                    service.Stop();
                    service.WaitForStatus(ServiceControllerStatus.Stopped, TimeSpan.FromSeconds(30));
                }

                service.Start();
                service.WaitForStatus(ServiceControllerStatus.Running, TimeSpan.FromSeconds(30));

                return new OperationResult
                {
                    IsSuccess = true,
                    Message = "服务重启成功"
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "重启服务时发生错误");
                return new OperationResult
                {
                    IsSuccess = false,
                    Message = $"服务重启失败: {ex.Message}"
                };
            }
        }

        private bool IsServiceRunning(string serviceName)
        {
            try
            {
                using var service = new ServiceController(serviceName);
                return service.Status == ServiceControllerStatus.Running;
            }
            catch
            {
                return false;
            }
        }

        private SystemPerformanceInfo GetPerformanceInfo()
        {
            try
            {
                var process = Process.GetCurrentProcess();
                
                return new SystemPerformanceInfo
                {
                    TotalMemoryUsage = process.WorkingSet64,
                    CpuUsage = 0, // 需要更复杂的逻辑来计算 CPU 使用率
                    DiskUsage = 0, // 需要获取磁盘使用情况
                    ActiveConnections = 0 // 需要获取活动连接数
                };
            }
            catch
            {
                return new SystemPerformanceInfo();
            }
        }
    }
}
