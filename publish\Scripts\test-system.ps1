# 文件监控系统测试脚本

param(
    [string]$TestDataPath = "C:\TestFiles",
    [string]$DatabaseConnectionString = "Server=127.0.0.1;Database=FileMonitorDB;User=root;Password=******;Charset=utf8mb4;",
    [int]$TestDurationSeconds = 60
)

Write-Host "=== 文件监控系统测试脚本 ===" -ForegroundColor Green
Write-Host "测试数据路径: $TestDataPath"
Write-Host "测试持续时间: $TestDurationSeconds 秒"
Write-Host ""

# 创建测试目录
if (-not (Test-Path $TestDataPath)) {
    Write-Host "创建测试目录..." -ForegroundColor Yellow
    New-Item -ItemType Directory -Path $TestDataPath -Force | Out-Null
}

try {
    # 测试数据库连接
    Write-Host "测试数据库连接..." -ForegroundColor Yellow
    
    # 这里可以添加数据库连接测试的代码
    # 由于需要 MySQL .NET 连接器，这里只是示例
    Write-Host "数据库连接测试 - 跳过（需要在实际环境中测试）" -ForegroundColor Gray
    
    # 测试文件操作监控
    Write-Host "测试文件操作监控..." -ForegroundColor Yellow
    
    $testFiles = @()
    $startTime = Get-Date
    
    Write-Host "开始生成测试文件操作..."
    
    while ((Get-Date).Subtract($startTime).TotalSeconds -lt $TestDurationSeconds) {
        try {
            # 创建测试文件
            $fileName = "test_$(Get-Random)_$(Get-Date -Format 'HHmmss').txt"
            $filePath = Join-Path $TestDataPath $fileName
            
            # 写入文件
            "测试数据 - $(Get-Date)" | Out-File -FilePath $filePath -Encoding UTF8
            $testFiles += $filePath
            
            # 读取文件
            Get-Content $filePath | Out-Null
            
            # 修改文件
            "修改后的数据 - $(Get-Date)" | Add-Content -Path $filePath
            
            # 重命名文件
            $newName = "renamed_$fileName"
            $newPath = Join-Path $TestDataPath $newName
            Rename-Item $filePath $newPath
            $testFiles = $testFiles | Where-Object { $_ -ne $filePath }
            $testFiles += $newPath
            
            Write-Host "." -NoNewline -ForegroundColor Green
            
            Start-Sleep -Milliseconds 500
            
        } catch {
            Write-Host "!" -NoNewline -ForegroundColor Red
        }
    }
    
    Write-Host ""
    Write-Host "文件操作测试完成，共创建 $($testFiles.Count) 个测试文件"
    
    # 测试敏感文件访问（触发告警）
    Write-Host "测试敏感文件访问告警..." -ForegroundColor Yellow
    
    $sensitiveFiles = @(
        "机密文档.docx",
        "财务报表.xlsx", 
        "confidential_data.pdf",
        "salary_info.txt"
    )
    
    foreach ($sensitiveFile in $sensitiveFiles) {
        $filePath = Join-Path $TestDataPath $sensitiveFile
        "敏感测试数据 - $(Get-Date)" | Out-File -FilePath $filePath -Encoding UTF8
        Get-Content $filePath | Out-Null
        $testFiles += $filePath
        Write-Host "创建敏感文件: $sensitiveFile" -ForegroundColor Cyan
    }
    
    # 测试批量删除操作
    Write-Host "测试批量删除操作..." -ForegroundColor Yellow
    
    $filesToDelete = $testFiles | Get-Random -Count ([Math]::Min(5, $testFiles.Count))
    foreach ($file in $filesToDelete) {
        if (Test-Path $file) {
            Remove-Item $file -Force
            Write-Host "删除文件: $(Split-Path $file -Leaf)" -ForegroundColor Red
        }
    }
    
    Write-Host ""
    Write-Host "=== 测试完成 ===" -ForegroundColor Green
    Write-Host ""
    Write-Host "测试结果检查:"
    Write-Host "1. 检查服务日志: Get-EventLog -LogName Application -Source FileMonitorService -Newest 20"
    Write-Host "2. 检查数据库记录: SELECT * FROM access_log ORDER BY event_time DESC LIMIT 20;"
    Write-Host "3. 检查告警记录: SELECT * FROM alert_history ORDER BY alert_time DESC LIMIT 10;"
    Write-Host ""
    Write-Host "如果看到以下类型的记录，说明监控正常工作:"
    Write-Host "- 文件创建 (Create) 操作"
    Write-Host "- 文件读取 (Read) 操作"
    Write-Host "- 文件写入 (Write) 操作"
    Write-Host "- 文件重命名 (Renamed) 操作"
    Write-Host "- 文件删除 (Delete) 操作"
    Write-Host "- 敏感文件访问告警"
    
} catch {
    Write-Error "测试过程中发生错误: $($_.Exception.Message)"
} finally {
    # 清理测试文件
    $cleanup = Read-Host "是否清理测试文件? (Y/N)"
    if ($cleanup -eq 'Y' -or $cleanup -eq 'y') {
        Write-Host "清理测试文件..." -ForegroundColor Yellow
        if (Test-Path $TestDataPath) {
            Remove-Item $TestDataPath -Recurse -Force
            Write-Host "测试文件已清理" -ForegroundColor Green
        }
    }
}

Write-Host ""
Write-Host "测试脚本执行完成！" -ForegroundColor Green
