using FileMonitor.Core.Database;
using FileMonitor.Core.Models;
using FileMonitor.Web.Models;
using Dapper;
using MySql.Data.MySqlClient;
using System.Text;

namespace FileMonitor.Web.Services
{
    public class LogService
    {
        private readonly string _connectionString;

        public LogService(DatabaseService databaseService)
        {
            _connectionString = databaseService.GetType()
                .GetField("_connectionString", System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance)?
                .GetValue(databaseService) as string ?? "";
        }

        public async Task<LogListViewModel> GetLogsAsync(LogFilterViewModel filter)
        {
            using var connection = new MySqlConnection(_connectionString);

            var whereClause = BuildWhereClause(filter);
            var parameters = BuildParameters(filter);

            // 获取总数
            var countSql = $"SELECT COUNT(*) FROM access_log {whereClause}";
            var totalCount = await connection.QuerySingleAsync<int>(countSql, parameters);

            // 获取分页数据
            var offset = (filter.Page - 1) * filter.PageSize;
            var dataSql = $@"
                SELECT * FROM access_log 
                {whereClause}
                ORDER BY event_time DESC 
                LIMIT @PageSize OFFSET @Offset";

            parameters.Add("PageSize", filter.PageSize);
            parameters.Add("Offset", offset);

            var logs = await connection.QueryAsync<FileAccessLog>(dataSql, parameters);

            return new LogListViewModel
            {
                Logs = logs.ToList(),
                Filter = filter,
                TotalCount = totalCount,
                TotalPages = (int)Math.Ceiling((double)totalCount / filter.PageSize)
            };
        }

        public async Task<FileAccessLog?> GetLogByIdAsync(int id)
        {
            using var connection = new MySqlConnection(_connectionString);
            
            var sql = "SELECT * FROM access_log WHERE id = @Id";
            return await connection.QuerySingleOrDefaultAsync<FileAccessLog>(sql, new { Id = id });
        }

        public async Task<byte[]> ExportLogsAsync(LogFilterViewModel filter)
        {
            using var connection = new MySqlConnection(_connectionString);

            var whereClause = BuildWhereClause(filter);
            var parameters = BuildParameters(filter);

            var sql = $@"
                SELECT 
                    event_time as '事件时间',
                    file_path as '文件路径',
                    action as '操作类型',
                    user_name as '用户名',
                    source_ip as '来源IP',
                    details as '详细信息'
                FROM access_log 
                {whereClause}
                ORDER BY event_time DESC";

            var logs = await connection.QueryAsync(sql, parameters);

            var csv = new StringBuilder();
            
            // 添加标题行
            csv.AppendLine("事件时间,文件路径,操作类型,用户名,来源IP,详细信息");

            // 添加数据行
            foreach (var log in logs)
            {
                csv.AppendLine($"{log.事件时间:yyyy-MM-dd HH:mm:ss}," +
                              $"\"{log.文件路径}\"," +
                              $"{log.操作类型}," +
                              $"{log.用户名}," +
                              $"{log.来源IP}," +
                              $"\"{log.详细信息}\"");
            }

            return Encoding.UTF8.GetBytes(csv.ToString());
        }

        public async Task<List<FileAccessLog>> SearchLogsAsync(LogSearchRequest request)
        {
            using var connection = new MySqlConnection(_connectionString);

            var sql = @"
                SELECT * FROM access_log 
                WHERE (file_path LIKE @SearchTerm 
                   OR user_name LIKE @SearchTerm 
                   OR action LIKE @SearchTerm)
                ORDER BY event_time DESC 
                LIMIT @Limit";

            var searchTerm = $"%{request.SearchTerm}%";
            var logs = await connection.QueryAsync<FileAccessLog>(sql, new 
            { 
                SearchTerm = searchTerm, 
                Limit = request.Limit 
            });

            return logs.ToList();
        }

        private string BuildWhereClause(LogFilterViewModel filter)
        {
            var conditions = new List<string>();

            if (!string.IsNullOrEmpty(filter.FilePath))
                conditions.Add("file_path LIKE @FilePath");

            if (!string.IsNullOrEmpty(filter.UserName))
                conditions.Add("user_name LIKE @UserName");

            if (!string.IsNullOrEmpty(filter.Action))
                conditions.Add("action = @Action");

            if (filter.StartDate.HasValue)
                conditions.Add("event_time >= @StartDate");

            if (filter.EndDate.HasValue)
                conditions.Add("event_time <= @EndDate");

            return conditions.Any() ? "WHERE " + string.Join(" AND ", conditions) : "";
        }

        private DynamicParameters BuildParameters(LogFilterViewModel filter)
        {
            var parameters = new DynamicParameters();

            if (!string.IsNullOrEmpty(filter.FilePath))
                parameters.Add("FilePath", $"%{filter.FilePath}%");

            if (!string.IsNullOrEmpty(filter.UserName))
                parameters.Add("UserName", $"%{filter.UserName}%");

            if (!string.IsNullOrEmpty(filter.Action))
                parameters.Add("Action", filter.Action);

            if (filter.StartDate.HasValue)
                parameters.Add("StartDate", filter.StartDate.Value);

            if (filter.EndDate.HasValue)
                parameters.Add("EndDate", filter.EndDate.Value);

            return parameters;
        }
    }
}
