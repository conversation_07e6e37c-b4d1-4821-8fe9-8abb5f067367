﻿using System;

namespace FileMonitor.Core.Models
{
    /// <summary>
    /// 文件访问日志实体类
    /// </summary>
    public class FileAccessLog
    {
        public int Id { get; set; }
        public DateTime EventTime { get; set; }
        public string FilePath { get; set; } = string.Empty;
        public string Action { get; set; } = string.Empty;
        public string UserName { get; set; } = string.Empty;
        public string SourceIP { get; set; } = string.Empty;
        public string Details { get; set; } = string.Empty;
    }

    /// <summary>
    /// 告警历史记录实体类
    /// </summary>
    public class AlertHistory
    {
        public int Id { get; set; }
        public int LogId { get; set; }
        public string RuleName { get; set; } = string.Empty;
        public DateTime AlertTime { get; set; }
        public string AlertMessage { get; set; } = string.Empty;
        public bool IsProcessed { get; set; }
    }
}
