﻿using FileMonitor.Core.Models;
using FileMonitor.Core.Rules;
using FileMonitor.Core.Configuration;
using Microsoft.Extensions.Logging;
using Moq;

namespace FileMonitor.Tests;

public class FileAccessLogTests
{
    [Fact]
    public void FileAccessLog_ShouldCreateWithCorrectProperties()
    {
        // Arrange
        var expectedTime = DateTime.Now;
        var expectedPath = @"C:\test\file.txt";
        var expectedAction = "Read";
        var expectedUser = "DOMAIN\\user";
        var expectedIP = "*************";

        // Act
        var log = new FileAccessLog
        {
            EventTime = expectedTime,
            FilePath = expectedPath,
            Action = expectedAction,
            UserName = expectedUser,
            SourceIP = expectedIP
        };

        // Assert
        Assert.Equal(expectedTime, log.EventTime);
        Assert.Equal(expectedPath, log.FilePath);
        Assert.Equal(expectedAction, log.Action);
        Assert.Equal(expectedUser, log.UserName);
        Assert.Equal(expectedIP, log.SourceIP);
    }
}

public class RuleEngineTests
{
    private readonly Mock<ILogger<RuleEngine>> _mockLogger;
    private readonly RuleEngine _ruleEngine;

    public RuleEngineTests()
    {
        _mockLogger = new Mock<ILogger<RuleEngine>>();
        _ruleEngine = new RuleEngine(_mockLogger.Object);
    }

    [Fact]
    public void RuleEngine_ShouldInitializeWithZeroRules()
    {
        // Assert
        Assert.Equal(0, _ruleEngine.GetRuleCount());
        Assert.Equal(0, _ruleEngine.GetEnabledRuleCount());
    }

    [Fact]
    public void CleanupThrottleCache_ShouldNotThrow()
    {
        // Act & Assert
        var exception = Record.Exception(() => _ruleEngine.CleanupThrottleCache());
        Assert.Null(exception);
    }
}

public class AppConfigTests
{
    [Fact]
    public void AppConfig_ShouldHaveDefaultValues()
    {
        // Act
        var config = new AppConfig();

        // Assert
        Assert.Empty(config.ConnectionString);
        Assert.Empty(config.MonitorPaths);
        Assert.True(config.EnableEventLogMonitoring);
        Assert.True(config.EnableFileSystemMonitoring);
        Assert.Equal("Information", config.LogLevel);
        Assert.NotNull(config.Email);
        Assert.NotNull(config.Performance);
    }

    [Fact]
    public void EmailConfig_ShouldHaveDefaultValues()
    {
        // Act
        var emailConfig = new EmailConfig();

        // Assert
        Assert.Empty(emailConfig.SmtpServer);
        Assert.Equal(587, emailConfig.SmtpPort);
        Assert.Empty(emailConfig.FromEmail);
        Assert.Empty(emailConfig.ToEmails);
        Assert.True(emailConfig.EnableSsl);
    }

    [Fact]
    public void PerformanceConfig_ShouldHaveDefaultValues()
    {
        // Act
        var perfConfig = new PerformanceConfig();

        // Assert
        Assert.Equal(100, perfConfig.BatchSize);
        Assert.Equal(30, perfConfig.BatchIntervalSeconds);
        Assert.Equal(1000, perfConfig.MaxCacheSize);
        Assert.Equal(90, perfConfig.LogRetentionDays);
    }
}
