{"Logging": {"LogLevel": {"Default": "Information", "Microsoft": "Warning", "Microsoft.Hosting.Lifetime": "Information", "FileMonitor": "Debug"}, "Console": {"IncludeScopes": false, "LogLevel": {"Default": "Information"}}, "EventLog": {"LogLevel": {"Default": "Warning"}, "SourceName": "FileMonitorService"}}, "ConnectionString": "Server=127.0.0.1;Database=FileMonitorDB;User=root;Password=******;Charset=utf8mb4;", "MonitorPaths": ["C:\\SharedFiles", "C:\\ImportantDocuments", "D:\\CompanyData"], "EnableEventLogMonitoring": true, "EnableFileSystemMonitoring": true, "LogLevel": "Information", "LogFilePath": "logs/filemonitor.log", "RulesConfigPath": "Rules/rules.yaml", "Email": {"SmtpServer": "smtp.company.com", "SmtpPort": 587, "FromEmail": "<EMAIL>", "FromPassword": "your_password_here", "ToEmails": ["<EMAIL>", "<EMAIL>"], "EnableSsl": true}, "Performance": {"BatchSize": 100, "BatchIntervalSeconds": 30, "MaxCacheSize": 1000, "LogRetentionDays": 90}}