using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using MySql.Data.MySqlClient;
using Dapper;
using FileMonitor.Core.Models;

namespace FileMonitor.Core.Database
{
    /// <summary>
    /// 数据库服务类，负责文件访问日志的数据操作
    /// </summary>
    public class DatabaseService
    {
        private readonly string _connectionString;

        public DatabaseService(string connectionString)
        {
            _connectionString = connectionString ?? throw new ArgumentNullException(nameof(connectionString));
        }

        /// <summary>
        /// 保存文件访问日志
        /// </summary>
        /// <param name="log">文件访问日志对象</param>
        /// <returns>插入的记录ID</returns>
        public async Task<int> SaveLogAsync(FileAccessLog log)
        {
            const string sql = @"
                INSERT INTO access_log 
                (event_time, file_path, action, user_name, source_ip, details) 
                VALUES (@EventTime, @FilePath, @Action, @UserName, @SourceIP, @Details);
                SELECT LAST_INSERT_ID();";

            using var connection = new MySqlConnection(_connectionString);
            var id = await connection.QuerySingleAsync<int>(sql, log);
            return id;
        }

        /// <summary>
        /// 批量保存文件访问日志
        /// </summary>
        /// <param name="logs">文件访问日志列表</param>
        public async Task SaveLogsAsync(IEnumerable<FileAccessLog> logs)
        {
            const string sql = @"
                INSERT INTO access_log 
                (event_time, file_path, action, user_name, source_ip, details) 
                VALUES (@EventTime, @FilePath, @Action, @UserName, @SourceIP, @Details)";

            using var connection = new MySqlConnection(_connectionString);
            using var transaction = await connection.BeginTransactionAsync();
            
            try
            {
                await connection.ExecuteAsync(sql, logs, transaction);
                await transaction.CommitAsync();
            }
            catch
            {
                await transaction.RollbackAsync();
                throw;
            }
        }

        /// <summary>
        /// 根据条件查询文件访问日志
        /// </summary>
        /// <param name="filePath">文件路径（可选）</param>
        /// <param name="userName">用户名（可选）</param>
        /// <param name="startTime">开始时间（可选）</param>
        /// <param name="endTime">结束时间（可选）</param>
        /// <param name="limit">返回记录数限制</param>
        /// <returns>文件访问日志列表</returns>
        public async Task<IEnumerable<FileAccessLog>> GetLogsAsync(
            string? filePath = null, 
            string? userName = null, 
            DateTime? startTime = null, 
            DateTime? endTime = null, 
            int limit = 100)
        {
            var sql = "SELECT * FROM access_log WHERE 1=1";
            var parameters = new DynamicParameters();

            if (!string.IsNullOrEmpty(filePath))
            {
                sql += " AND file_path LIKE @FilePath";
                parameters.Add("FilePath", $"%{filePath}%");
            }

            if (!string.IsNullOrEmpty(userName))
            {
                sql += " AND user_name = @UserName";
                parameters.Add("UserName", userName);
            }

            if (startTime.HasValue)
            {
                sql += " AND event_time >= @StartTime";
                parameters.Add("StartTime", startTime.Value);
            }

            if (endTime.HasValue)
            {
                sql += " AND event_time <= @EndTime";
                parameters.Add("EndTime", endTime.Value);
            }

            sql += " ORDER BY event_time DESC LIMIT @Limit";
            parameters.Add("Limit", limit);

            using var connection = new MySqlConnection(_connectionString);
            return await connection.QueryAsync<FileAccessLog>(sql, parameters);
        }

        /// <summary>
        /// 保存告警记录
        /// </summary>
        /// <param name="alert">告警记录对象</param>
        /// <returns>插入的记录ID</returns>
        public async Task<int> SaveAlertAsync(AlertHistory alert)
        {
            const string sql = @"
                INSERT INTO alert_history 
                (log_id, rule_name, alert_time, alert_message, is_processed) 
                VALUES (@LogId, @RuleName, @AlertTime, @AlertMessage, @IsProcessed);
                SELECT LAST_INSERT_ID();";

            using var connection = new MySqlConnection(_connectionString);
            var id = await connection.QuerySingleAsync<int>(sql, alert);
            return id;
        }

        /// <summary>
        /// 测试数据库连接
        /// </summary>
        /// <returns>连接是否成功</returns>
        public async Task<bool> TestConnectionAsync()
        {
            try
            {
                using var connection = new MySqlConnection(_connectionString);
                await connection.OpenAsync();
                await connection.QuerySingleAsync<int>("SELECT 1");
                return true;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// 初始化数据库表结构
        /// </summary>
        public async Task InitializeDatabaseAsync()
        {
            const string createAccessLogTable = @"
                CREATE TABLE IF NOT EXISTS access_log (
                    id INT PRIMARY KEY AUTO_INCREMENT,
                    event_time DATETIME NOT NULL,
                    file_path TEXT NOT NULL,
                    action VARCHAR(50),
                    user_name VARCHAR(255),
                    source_ip VARCHAR(45),
                    details TEXT,
                    INDEX idx_event_time (event_time),
                    INDEX idx_user_action (user_name, action)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;";

            const string createAlertHistoryTable = @"
                CREATE TABLE IF NOT EXISTS alert_history (
                    id INT PRIMARY KEY AUTO_INCREMENT,
                    log_id INT,
                    rule_name VARCHAR(255),
                    alert_time DATETIME NOT NULL,
                    alert_message TEXT,
                    is_processed BOOLEAN DEFAULT FALSE,
                    INDEX idx_alert_time (alert_time),
                    INDEX idx_rule_name (rule_name),
                    FOREIGN KEY (log_id) REFERENCES access_log(id)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;";

            using var connection = new MySqlConnection(_connectionString);
            await connection.ExecuteAsync(createAccessLogTable);
            await connection.ExecuteAsync(createAlertHistoryTable);
        }
    }
}
