-- 文件监控系统数据库初始化脚本
-- 使用MySQL 8.0+

-- 创建数据库
CREATE DATABASE IF NOT EXISTS FileMonitorDB 
CHARACTER SET utf8mb4 
COLLATE utf8mb4_unicode_ci;

USE FileMonitorDB;

-- 创建文件访问日志表
CREATE TABLE IF NOT EXISTS access_log (
    id INT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
    event_time DATETIME NOT NULL COMMENT '事件发生时间',
    file_path TEXT NOT NULL COMMENT '文件路径',
    action VARCHAR(50) COMMENT '操作类型：Read/Write/Delete/Create/Rename',
    user_name VARCHAR(255) COMMENT '用户名',
    source_ip VARCHAR(45) COMMENT '来源IP地址',
    details TEXT COMMENT '详细信息（JSON格式）',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '记录创建时间',
    
    -- 索引优化
    INDEX idx_event_time (event_time),
    INDEX idx_user_action (user_name, action),
    INDEX idx_file_path (file_path(255)),
    INDEX idx_source_ip (source_ip),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB 
DEFAULT CHARSET=utf8mb4 
COLLATE=utf8mb4_unicode_ci 
COMMENT='文件访问日志表';

-- 创建告警历史表
CREATE TABLE IF NOT EXISTS alert_history (
    id INT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
    log_id INT COMMENT '关联的日志ID',
    rule_name VARCHAR(255) NOT NULL COMMENT '触发的规则名称',
    alert_time DATETIME NOT NULL COMMENT '告警时间',
    alert_message TEXT COMMENT '告警消息',
    is_processed BOOLEAN DEFAULT FALSE COMMENT '是否已处理',
    processed_by VARCHAR(255) COMMENT '处理人',
    processed_at DATETIME COMMENT '处理时间',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '记录创建时间',
    
    -- 索引优化
    INDEX idx_alert_time (alert_time),
    INDEX idx_rule_name (rule_name),
    INDEX idx_is_processed (is_processed),
    INDEX idx_created_at (created_at),
    
    -- 外键约束
    FOREIGN KEY (log_id) REFERENCES access_log(id) ON DELETE SET NULL
) ENGINE=InnoDB 
DEFAULT CHARSET=utf8mb4 
COLLATE=utf8mb4_unicode_ci 
COMMENT='告警历史表';

-- 创建规则配置表（可选，如果不使用YAML文件）
CREATE TABLE IF NOT EXISTS monitor_rules (
    id INT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
    rule_name VARCHAR(255) NOT NULL UNIQUE COMMENT '规则名称',
    rule_type VARCHAR(50) NOT NULL COMMENT '规则类型：file_pattern/user_pattern/time_pattern',
    pattern_value TEXT NOT NULL COMMENT '匹配模式',
    action_filter VARCHAR(100) COMMENT '操作过滤器',
    alert_level VARCHAR(20) DEFAULT 'Medium' COMMENT '告警级别：Low/Medium/High/Critical',
    is_enabled BOOLEAN DEFAULT TRUE COMMENT '是否启用',
    description TEXT COMMENT '规则描述',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    INDEX idx_rule_type (rule_type),
    INDEX idx_is_enabled (is_enabled),
    INDEX idx_alert_level (alert_level)
) ENGINE=InnoDB 
DEFAULT CHARSET=utf8mb4 
COLLATE=utf8mb4_unicode_ci 
COMMENT='监控规则配置表';

-- 插入示例规则
INSERT INTO monitor_rules (rule_name, rule_type, pattern_value, action_filter, alert_level, description) VALUES
('敏感文件访问', 'file_pattern', '.*[机密|保密|confidential|secret].*\.(xlsx?|docx?|pdf)$', 'Read,Write', 'High', '检测对包含敏感关键词的文件访问'),
('非工作时间访问', 'time_pattern', '18:00-08:00', 'Read,Write,Delete', 'Medium', '检测非工作时间的文件访问'),
('批量删除操作', 'action_pattern', 'Delete', NULL, 'Critical', '检测批量删除操作'),
('外部IP访问', 'ip_pattern', '^(?!192\.168\.|10\.|172\.(1[6-9]|2[0-9]|3[01])\.).*', 'Read,Write', 'High', '检测来自外部IP的文件访问');

-- 创建系统配置表
CREATE TABLE IF NOT EXISTS system_config (
    id INT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
    config_key VARCHAR(100) NOT NULL UNIQUE COMMENT '配置键',
    config_value TEXT COMMENT '配置值',
    config_type VARCHAR(20) DEFAULT 'string' COMMENT '配置类型：string/int/bool/json',
    description TEXT COMMENT '配置描述',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    INDEX idx_config_key (config_key)
) ENGINE=InnoDB 
DEFAULT CHARSET=utf8mb4 
COLLATE=utf8mb4_unicode_ci 
COMMENT='系统配置表';

-- 插入默认系统配置
INSERT INTO system_config (config_key, config_value, config_type, description) VALUES
('log_retention_days', '90', 'int', '日志保留天数'),
('batch_size', '100', 'int', '批量处理大小'),
('alert_email_enabled', 'true', 'bool', '是否启用邮件告警'),
('monitor_enabled', 'true', 'bool', '是否启用监控');

-- 创建性能统计表
CREATE TABLE IF NOT EXISTS performance_stats (
    id INT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
    stat_date DATE NOT NULL COMMENT '统计日期',
    total_events INT DEFAULT 0 COMMENT '总事件数',
    total_alerts INT DEFAULT 0 COMMENT '总告警数',
    unique_users INT DEFAULT 0 COMMENT '独立用户数',
    unique_files INT DEFAULT 0 COMMENT '独立文件数',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    
    UNIQUE KEY uk_stat_date (stat_date),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB 
DEFAULT CHARSET=utf8mb4 
COLLATE=utf8mb4_unicode_ci 
COMMENT='性能统计表';

-- 创建用于清理旧数据的存储过程
DELIMITER //
CREATE PROCEDURE CleanOldLogs(IN retention_days INT)
BEGIN
    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        ROLLBACK;
        RESIGNAL;
    END;
    
    START TRANSACTION;
    
    -- 删除旧的告警记录
    DELETE FROM alert_history 
    WHERE created_at < DATE_SUB(NOW(), INTERVAL retention_days DAY);
    
    -- 删除旧的访问日志
    DELETE FROM access_log 
    WHERE created_at < DATE_SUB(NOW(), INTERVAL retention_days DAY);
    
    COMMIT;
    
    SELECT CONCAT('清理完成，删除了 ', ROW_COUNT(), ' 条记录') AS result;
END //
DELIMITER ;

-- 创建用于生成性能统计的存储过程
DELIMITER //
CREATE PROCEDURE GenerateDailyStats(IN target_date DATE)
BEGIN
    INSERT INTO performance_stats (stat_date, total_events, total_alerts, unique_users, unique_files)
    SELECT 
        target_date,
        COUNT(*) as total_events,
        (SELECT COUNT(*) FROM alert_history WHERE DATE(alert_time) = target_date) as total_alerts,
        COUNT(DISTINCT user_name) as unique_users,
        COUNT(DISTINCT file_path) as unique_files
    FROM access_log 
    WHERE DATE(event_time) = target_date
    ON DUPLICATE KEY UPDATE
        total_events = VALUES(total_events),
        total_alerts = VALUES(total_alerts),
        unique_users = VALUES(unique_users),
        unique_files = VALUES(unique_files);
END //
DELIMITER ;

-- 显示创建结果
SELECT 'Database initialization completed successfully!' as status;
SHOW TABLES;
