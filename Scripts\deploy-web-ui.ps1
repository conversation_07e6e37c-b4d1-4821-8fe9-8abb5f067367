# 部署文件监控系统 Web UI
# 编译并部署 Web 管理界面

param(
    [string]$Configuration = "Release",
    [string]$OutputPath = "",
    [string]$Port = "80",
    [switch]$InstallAsService = $false,
    [switch]$StartAfterDeploy = $false
)

$ErrorActionPreference = "Stop"

# 获取脚本目录和项目根目录
$ScriptDir = Split-Path -Parent $MyInvocation.MyCommand.Definition
$ProjectRoot = Split-Path -Parent $ScriptDir

# 设置默认输出路径
if ([string]::IsNullOrEmpty($OutputPath)) {
    $OutputPath = Join-Path $ProjectRoot "publish-web"
}

Write-Host "=== 文件监控系统 Web UI 部署脚本 ===" -ForegroundColor Green
Write-Host "项目根目录: $ProjectRoot"
Write-Host "配置: $Configuration"
Write-Host "输出路径: $OutputPath"
Write-Host "端口: $Port"
Write-Host ""

try {
    # 切换到项目根目录
    Set-Location $ProjectRoot
    
    # 清理之前的构建
    Write-Host "清理之前的构建..." -ForegroundColor Yellow
    if (Test-Path $OutputPath) {
        Remove-Item $OutputPath -Recurse -Force
    }
    
    # 还原 NuGet 包
    Write-Host "还原 NuGet 包..." -ForegroundColor Yellow
    dotnet restore
    if ($LASTEXITCODE -ne 0) {
        throw "NuGet 包还原失败"
    }
    
    # 构建解决方案
    Write-Host "构建解决方案..." -ForegroundColor Yellow
    dotnet build --configuration $Configuration --no-restore
    if ($LASTEXITCODE -ne 0) {
        throw "构建失败"
    }
    
    # 发布 Web 项目
    Write-Host "发布 Web 项目..." -ForegroundColor Yellow
    $publishArgs = @(
        "publish"
        "FileMonitor.Web"
        "--configuration", $Configuration
        "--output", $OutputPath
        "--no-build"
        "--self-contained", "false"
    )
    
    & dotnet @publishArgs
    if ($LASTEXITCODE -ne 0) {
        throw "发布失败"
    }
    
    # 复制配置文件和资源
    Write-Host "复制配置文件和资源..." -ForegroundColor Yellow
    
    # 复制规则配置
    $rulesSource = Join-Path $ProjectRoot "Rules"
    $rulesTarget = Join-Path $OutputPath "Rules"
    if (Test-Path $rulesSource) {
        Copy-Item $rulesSource $rulesTarget -Recurse -Force
    }
    
    # 复制数据库脚本
    $dbSource = Join-Path $ProjectRoot "Database"
    $dbTarget = Join-Path $OutputPath "Database"
    if (Test-Path $dbSource) {
        Copy-Item $dbSource $dbTarget -Recurse -Force
    }
    
    # 创建日志目录
    $logsDir = Join-Path $OutputPath "logs"
    if (-not (Test-Path $logsDir)) {
        New-Item -ItemType Directory -Path $logsDir -Force | Out-Null
    }
    
    # 创建启动脚本
    $startScript = @"
@echo off
echo 启动文件监控系统 Web UI...
echo 访问地址: http://localhost:$Port
echo 按 Ctrl+C 停止服务
echo.
dotnet FileMonitor.Web.dll --urls "http://*:$Port"
pause
"@
    
    $startScript | Out-File -FilePath (Join-Path $OutputPath "start.bat") -Encoding ASCII
    
    # 创建 PowerShell 启动脚本
    $psStartScript = @"
# 启动文件监控系统 Web UI
Write-Host "启动文件监控系统 Web UI..." -ForegroundColor Green
Write-Host "访问地址: http://localhost:$Port" -ForegroundColor Cyan
Write-Host "按 Ctrl+C 停止服务" -ForegroundColor Yellow
Write-Host ""

try {
    dotnet FileMonitor.Web.dll --urls "http://*:$Port"
}
catch {
    Write-Error "启动失败: `$(`$_.Exception.Message)"
    Read-Host "按回车键退出"
}
"@
    
    $psStartScript | Out-File -FilePath (Join-Path $OutputPath "start.ps1") -Encoding UTF8
    
    # 创建配置说明文件
    $configReadme = @"
# 文件监控系统 Web UI 配置说明

## 快速启动

### Windows
双击 `start.bat` 或运行 `start.ps1`

### 命令行
```bash
dotnet FileMonitor.Web.dll --urls "http://*:$Port"
```

## 配置文件

### appsettings.json
主配置文件，包含数据库连接、监控路径等设置。

### appsettings.Production.json
生产环境配置文件，会覆盖主配置文件中的相应设置。

## 重要配置项

1. **数据库连接**
   ```json
   "ConnectionString": "Server=127.0.0.1;Database=FileMonitorDB;User=root;Password=******;"
   ```

2. **监控路径**
   ```json
   "MonitorPaths": [
     "C:\\SharedFiles",
     "C:\\ImportantDocuments"
   ]
   ```

3. **邮件配置**
   ```json
   "Email": {
     "SmtpServer": "smtp.company.com",
     "FromEmail": "<EMAIL>"
   }
   ```

## 访问地址

- 本地访问: http://localhost:$Port
- 网络访问: http://[服务器IP]:$Port

## 功能模块

- 📊 **仪表板**: 系统概览和实时统计
- 📋 **访问日志**: 查看和搜索文件访问记录  
- 🚨 **告警管理**: 查看和处理安全告警
- ⚙️ **系统管理**: 配置和监控系统状态

## 故障排除

1. **端口被占用**
   - 修改启动脚本中的端口号
   - 或使用命令: `dotnet FileMonitor.Web.dll --urls "http://*:其他端口"`

2. **数据库连接失败**
   - 检查 MySQL 服务是否运行
   - 验证连接字符串中的用户名和密码
   - 确认数据库已初始化

3. **权限问题**
   - 确保应用有读写日志目录的权限
   - 如需监听 80 端口，需要管理员权限

## 日志文件

- 应用日志: logs/filemonitor.log
- 系统日志: Windows 事件查看器 -> 应用程序日志
"@
    
    $configReadme | Out-File -FilePath (Join-Path $OutputPath "README.md") -Encoding UTF8
    
    Write-Host "部署完成！" -ForegroundColor Green
    Write-Host "部署路径: $OutputPath"
    
    # 显示部署的文件
    Write-Host ""
    Write-Host "部署的文件:" -ForegroundColor Cyan
    Get-ChildItem $OutputPath -Recurse | Where-Object { -not $_.PSIsContainer } | 
        Select-Object FullName | ForEach-Object { 
            $relativePath = $_.FullName.Substring($OutputPath.Length + 1)
            Write-Host "  $relativePath" -ForegroundColor Gray
        }
    
    # 安装为服务（如果指定）
    if ($InstallAsService) {
        Write-Host ""
        Write-Host "安装为 Windows 服务..." -ForegroundColor Yellow
        # 这里可以添加安装为 Windows 服务的逻辑
        Write-Host "注意: 安装为服务需要额外的配置，请参考 ASP.NET Core 服务部署文档" -ForegroundColor Yellow
    }
    
    # 启动服务（如果指定）
    if ($StartAfterDeploy) {
        Write-Host ""
        Write-Host "启动 Web 服务..." -ForegroundColor Yellow
        
        Set-Location $OutputPath
        Start-Process -FilePath "dotnet" -ArgumentList "FileMonitor.Web.dll", "--urls", "http://*:$Port" -NoNewWindow
        
        # 等待服务启动
        Start-Sleep -Seconds 3
        
        try {
            $response = Invoke-WebRequest -Uri "http://localhost:$Port" -TimeoutSec 5 -UseBasicParsing
            Write-Host "✅ Web 服务启动成功！" -ForegroundColor Green
            Write-Host "访问地址: http://localhost:$Port" -ForegroundColor Cyan
        }
        catch {
            Write-Warning "无法验证服务启动状态"
        }
    }
    
} catch {
    Write-Error "部署过程中发生错误: $($_.Exception.Message)"
    exit 1
} finally {
    # 恢复原始位置
    Set-Location $ScriptDir
}

Write-Host ""
Write-Host "=== 部署完成 ===" -ForegroundColor Green
Write-Host ""
Write-Host "下一步操作:"
Write-Host "1. 检查并修改配置文件: $(Join-Path $OutputPath 'appsettings.json')"
Write-Host "2. 确保数据库服务正在运行"
Write-Host "3. 启动 Web 服务: $(Join-Path $OutputPath 'start.bat')"
Write-Host "4. 访问管理界面: http://localhost:$Port"
